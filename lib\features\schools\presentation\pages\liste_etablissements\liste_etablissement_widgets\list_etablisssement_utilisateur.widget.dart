import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/schools/presentation/bloc/financial_status_cubit.dart';
import 'package:Kairos/features/schools/presentation/bloc/financial_status_state.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'dart:convert'; // Import for base64 decoding
import 'alert.widget.dart';
import 'package:Kairos/features/profile/data/profile_type.enum.dart'; // Import the ProfileType enum

class ListeEtablissementUtilisateurWidget extends StatefulWidget {
  final List<EtablissementUtilisateur> userSchools; // Accept list of user schools


  const ListeEtablissementUtilisateurWidget({super.key, required this.userSchools});

  @override
  State<ListeEtablissementUtilisateurWidget> createState() => _ListeEtablissementUtilisateurWidgetState();
}

class _ListeEtablissementUtilisateurWidgetState extends State<ListeEtablissementUtilisateurWidget> {
  EtablissementUtilisateur? _currentSchool;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.userSchools.length, // Use the provided list
      itemBuilder: (context, index) {
        final school = widget.userSchools[index];
        return BlocListener<FinancialStatusCubit, FinancialStatusState>(
          listener: (context, state) {
            if (state is FinancialStatusSuccess && _currentSchool?.codeEtab == school.codeEtab) {
              if(state.financialStatus.username != school.codeUtilisateur){
                return;
              }
              debugPrint('FinancialStatusSuccess --->: \\${state.financialStatus.enRegle}');
              if (!state.financialStatus.enRegle && state.financialStatus.username == school.codeUtilisateur) {
                showDialog(
                  context: context,
                  builder: (context) => AlertWidget(
                    message: "Vous avez des frais impayés. Veuillez régulariser votre situation afin d'accèder á votre espace",
                    school: school,
                  ),
                );
              } else {
                if (_currentSchool?.profil == ProfileType.etudiant.code) {
                   _navigateToDashboard(context);
                } else if (_currentSchool?.profil == ProfileType.tuteur.code) {
                  _navigateToDossierSelection(context);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text("Accès autorisé mais profil non reconnu: fontionnalité en cours de développement")),
                  );
                }
              }
            } else if (state is FinancialStatusError && _currentSchool?.codeEtab == school.codeEtab) {
              debugPrint('FinancialStatusError: \\${state.message}');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("Erreur: \\${state.message}")),
              );
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(
                color: Theme.of(context).colorScheme.secondary,
                width: 1.0,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.7),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(7.0),
              // Prevent tap when financial status is loading
              onTap: () async {
                // Check if the cubit is currently loading
                if(school.profil == ProfileType.etudiant.code){

                final currentState = context.read<FinancialStatusCubit>().state;
                if (currentState is! FinancialStatusLoading) {
                  // Only handle selection if not loading
                  await _handleSchoolSelection(context, school);
                }
                } else {
                  setState(() {
                    _currentSchool = school;
                  _navigateToDossierSelection(context);
                  });
                }
              },
              child: Padding(
                padding: const EdgeInsets.only(left: 10.0, right: 16.0, top: 7.0, bottom: 7.0),
                child: Row(
                  children: [
                    // School Logo
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        border: Border.all(
                          color: Colors.grey.shade200,
                          width: 1.0,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: school.logoEtablissement.isNotEmpty
                            ? Image.memory(
                                base64Decode(school.logoEtablissement),
                                width: 50,
                                height: 50,
                                fit: BoxFit.contain,
                              )
                            : Image.asset(
                                "assets/images/logo_iam.png",
                                width: 50,
                                height: 50,
                                fit: BoxFit.contain,
                              ),
                      ),
                    ),
                    const SizedBox(width: 16.0),
                    // School Information
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // School Name (Uppercase)
                          Text(
                            school.libelleEtab.toUpperCase(),
                            style: const TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4.0),
                          // Subtitle with person icon
                          Row(
                            children: [
                              Icon(
                                Icons.person,
                                size: 24.0,
                                color: Colors.black,
                              ),
                              const SizedBox(width: 4.0),
                              Expanded(
                                child: Text.rich(
                                  TextSpan(
                                    children: [
                                      const TextSpan(text: "AVEC LE PROFIL ", style: TextStyle(fontSize: 10)),
                                      TextSpan(
                                        text: school.profil == 'ETU'
                                            ? 'ÉTUDIANT'
                                            : school.profil == 'PAR'
                                                ? 'PARENT'
                                                : school.profil == 'TUT'
                                                    ? 'TUTEUR'
                                                    : 'ÉTUDIANT',
                                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 10),
                                      ),
                                      const TextSpan(text: " SOUS L'ID ", style: TextStyle(fontSize: 10)),
                                      TextSpan(
                                        text: school.codeUtilisateur,
                                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 10),
                                      ),
                                    ],
                                    style: TextStyle(
                                      fontSize: 12.0,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Trailing Icon or Loading Indicator
                    BlocBuilder<FinancialStatusCubit, FinancialStatusState>(
                      builder: (context, state) {
                        if (state is FinancialStatusLoading && (state).codeEtudiant == school.codeUtilisateur) {
                          // Show spinner when loading
                          return const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          );
                        } else {
                          // Show arrow icon otherwise
                          return Icon(
                            Icons.arrow_forward_ios,
                            size: 16.0,
                            color: Colors.grey.shade400,
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Handle school selection and check financial status
  Future<void> _handleSchoolSelection(BuildContext context, EtablissementUtilisateur school) async {
    try {
      // Store the current school for later navigation
      setState(() {
        _currentSchool = school;
      });

      // Get phone number from SharedPreferences
      final authLocalDataSource = sl<AuthLocalDataSource>();
      final phoneNumber = await authLocalDataSource.getPhoneNumber();

      if (!context.mounted) return;

      if (phoneNumber != null) {
        // Trigger financial status check via BLoC
        context.read<FinancialStatusCubit>().checkFinancialStatus(
          codeEtab: school.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: school.codeUtilisateur,
        );
      } else {
        // Handle case where phone number is not available
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur: Numéro de téléphone non disponible")),
        );
      }
    } catch (e) {
      // Handle any errors
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur: $e")),
        );
      }
    }
  }

  /// Navigate to dashboard with school data
  void _navigateToDashboard(BuildContext context) {
    if (_currentSchool != null) {
      debugPrint('Navigating to dashboard with school: ${_currentSchool!.libelleEtab}');
        Navigator.pushNamed(
          context,
          '/dashboard',
          arguments: {
            'school': _currentSchool,
          },
        ); // Navigate to dashboard with school data and arguments provided in the route settings.dart file. This is where the dashboard page is defined. The dashboard page will receive the school data as an argument.
    }
  }

  /// Navigate to dossier selection page with school data
  void _navigateToDossierSelection(BuildContext context) {
    if (_currentSchool != null) {
      debugPrint('Navigating to dossier selection with school: ${_currentSchool!.libelleEtab}');
        Navigator.pushNamed(
          context,
          '/dossier_selection',
          arguments: _currentSchool,
        );
    }
  }
}