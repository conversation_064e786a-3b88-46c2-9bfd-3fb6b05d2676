import 'package:Kairos/features/student_records/domain/entities/dossier_attachment_entity.dart'; 

/// Data model for a dossier attachment file
class DossierAttachmentModel {
  final String file; // base64 encoded file content
  final String type; // MIME type (e.g., "application/pdf")
  final String returnCode; // API return code (e.g., "SUCCESS")

  const DossierAttachmentModel({
    required this.file,
    required this.type,
    required this.returnCode,
  });

  /// Factory constructor to create DossierAttachmentModel from JSON
  factory DossierAttachmentModel.fromJson(Map<String, dynamic> json) {
    return DossierAttachmentModel(
      file: json['file'] ?? '',
      type: json['type'] ?? '',
      returnCode: json['returnCode'] ?? '',
    );
  }

  /// Method to convert DossierAttachmentModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'file': file,
      'type': type,
      'returnCode': returnCode,
    };
  }

  /// Method to convert DossierAttachmentModel to DossierAttachmentEntity
  DossierAttachmentEntity toEntity() {
    return DossierAttachmentEntity(
      file: file,
      type: type,
      returnCode: returnCode,
    );
  }
}
