import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/course_log_entity.dart';

/// Repository interface for course log operations
abstract class CourseLogRepository {
  /// Get course log data
  Future<Either<Failure, List<List<CourseLogEntity>>>> getCourseLog({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get annual course log data with year filtering
  Future<Either<Failure, List<List<CourseLogEntity>>>> getCourseLogAnnuel({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required int annee,
  });
}