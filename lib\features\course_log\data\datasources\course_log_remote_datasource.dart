import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/error/exceptions.dart';
import '../models/course_log_model.dart';

/// Remote data source interface for course log operations
abstract class CourseLogRemoteDataSource {
  /// Get course log data from API
  Future<List<List<CourseLogModel>>> getCourseLog({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get annual course log data with year filtering from API
  Future<List<List<CourseLogModel>>> getCourseLogAnnuel({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required int annee,
  });
}

/// Implementation of CourseLogRemoteDataSource
class CourseLogRemoteDataSourceImpl implements CourseLogRemoteDataSource {
  final ApiClient apiClient;

  CourseLogRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<List<CourseLogModel>>> getCourseLog({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final Map<String, String> queryParams = {
        'codeEtab': codeEtab,
        'numeroTel': telephone.replaceAll("+",""),
        'codeEtudiant': codeEtudiant,
      };

      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }

      print('CourseLogRemoteDataSource - getCourseLog called with params: $queryParams');

      final response = await apiClient.getWithToken(
        ApiEndpoints.cahierTexte,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes)
      );

      final String responseString = latin1.decode(response.data);
      debugPrint('CourseLogRemoteDataSource - Raw response: $responseString');

      final jsonResponse = json.decode(responseString);
      print('CourseLogRemoteDataSource - Parsed JSON: $jsonResponse');

// Parse response as map of date -> list of items
      if (jsonResponse is Map<String, dynamic>) {
        final entries = jsonResponse.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key)); // sort by date ascending
        return entries.map<List<CourseLogModel>>((entry) {
          final courseLog = entry.value;
          if (courseLog is List) {
            return courseLog.map<CourseLogModel>((item) => CourseLogModel.fromJson(item)).toList();
          }
          return <CourseLogModel>[];
        }).toList();
      }

      return [];
    } catch (e) {
      print('CourseLogRemoteDataSource - Error in getCourseLog: $e');
      throw ServerException('Failed to fetch course log data: $e');
    }
  }

  @override
  Future<List<List<CourseLogModel>>> getCourseLogAnnuel({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required int annee,
  }) async {
    try {
      // Build query parameters
      final Map<String, String> queryParams = {
        'codeEtab': codeEtab,
        'numeroTel': telephone.replaceAll("+",""),
        'codeEtudiant': codeEtudiant,
      };

      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }

      // Add year parameter for annual filtering
      queryParams['annee'] = annee.toString();

      print('CourseLogRemoteDataSource - getCourseLogAnnuel called with params: $queryParams');

      final response = await apiClient.getWithToken(
        ApiEndpoints.cahierTexteAnnuel,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes)
      );


      final String responseString = latin1.decode(response.data);
      debugPrint('CourseLogRemoteDataSource - Raw response: $responseString');

      final jsonResponse = json.decode(responseString);
      print('CourseLogRemoteDataSource - Parsed JSON: $jsonResponse');

     // Parse response as map of date -> list of items
      if (jsonResponse is Map<String, dynamic>) {
        final entries = jsonResponse.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key)); // sort by date ascending
        return entries.map<List<CourseLogModel>>((entry) {
          final courseLog = entry.value;
          if (courseLog is List) {
            return courseLog.map<CourseLogModel>((item) => CourseLogModel.fromJson(item)).toList();
          }
          return <CourseLogModel>[];
        }).toList();
      }

      return [];
    } catch (e) {
      print('CourseLogRemoteDataSource - Error in getCourseLogAnnuel: $e');
      throw ServerException('Failed to fetch annual course log data: $e');
    }
  }
}