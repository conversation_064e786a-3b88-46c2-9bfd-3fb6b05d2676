import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/core/usecases/usecase.dart';
import 'package:Kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';
import 'package:Kairos/features/student_records/domain/repositories/dossier_repository.dart';

class GetDossierAttachmentUseCase implements UseCase<DossierAttachmentEntity, GetDossierAttachmentParams> {
  final DossierRepository repository;

  GetDossierAttachmentUseCase(this.repository);

  @override
  Future<Either<Failure, DossierAttachmentEntity>> call(
      GetDossierAttachmentParams params) async {
    return await repository.getDossierAttachment(
      codeEtab: params.codeEtab,
      numeroTel: params.numeroTel,
      codeEtudiant: params.codeEtudiant,
      idObject: params.idObject,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

class GetDossierAttachmentParams extends Equatable {
  final String codeEtab;
  final String numeroTel;
  final String codeEtudiant;
  final int idObject;
  final String? codeUtilisateur;

  const GetDossierAttachmentParams({
    required this.codeEtab,
    required this.numeroTel,
    required this.codeEtudiant,
    required this.idObject,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props =>
      [codeEtab, numeroTel, codeEtudiant, idObject, codeUtilisateur];
}
