import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/core/widgets/common/empty_message.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';

import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'package:Kairos/features/course_log/domain/entities/course_log_entity.dart';
import '../../bloc/course_log_cubit.dart';
import '../../bloc/course_log_state.dart';

import 'cahier_texte_widgets/course_log_day_group.widget.dart';

class CahierTextePage extends StatefulWidget {
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const CahierTextePage({
    super.key,
    this.school,
    this.etudiant,
  });

  @override
  State<CahierTextePage> createState() => _CahierTextePageState();
}

class _CahierTextePageState extends State<CahierTextePage> {
  bool _isSearchBarVisible = false;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  int? _selectedYear;

  late AuthLocalDataSource _authLocalDataSource;

  @override
  void initState() {
    super.initState();
    _authLocalDataSource = sl<AuthLocalDataSource>();
    _searchController.addListener(_onSearchChanged);
    _loadCourseLogData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _searchController.clear();
        _searchQuery = '';
      }
    });
  }

  Future<void> _loadCourseLogData() async {
    try {
      // Get the school code from widget or fallback to a default
      final codeEtab = widget.school?.codeEtab ?? '';

      // Get phone number from AuthLocalDataSource
      final telephone = await _authLocalDataSource.getPhoneNumber() ?? '';

      // For ETU users, codeEtudiant is the same as codeUtilisateur
      // For PAR users, codeEtudiant comes from the selected child
      final codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.school?.codeUtilisateur ?? '';
      final codeUtilisateur = widget.etudiant != null ? widget.school?.codeUtilisateur : null;
      debugPrint('CahierTextePage - Loading course log data with:');
      debugPrint('  codeEtab: $codeEtab');
      debugPrint('  phoneNumber: $telephone');
      debugPrint('  codeEtudiant: $codeEtudiant');
      debugPrint('  codeUtilisateur: $codeUtilisateur');
      
      if (mounted && codeEtab.isNotEmpty && telephone.isNotEmpty && codeEtudiant.isNotEmpty) {
         context.read<CourseLogCubit>().loadCourseLogData(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );
     } else {
        debugPrint('Missing required parameters for schedule data loading: codeEtab=$codeEtab, telephone=$telephone, codeEtudiant=$codeEtudiant');
      }
    } catch (e) {
      debugPrint('Error loading schedule data: $e');
    }
  }

  // Method to handle year filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    // Extract year from startDate if available
    debugPrint("dateRange: $dateRange");
    if (dateRange['endDate'] != null && dateRange['endDate']!.isNotEmpty) {
      try {
          final year = int.parse(dateRange['endDate']!);
          setState(() {
            _selectedYear = year;
          });
          _loadFilteredCourseLogData();
      } catch (e) {
        debugPrint('Error parsing year from date: $e');
      }
    }
  }

  void _onClearDateFilter() {
    setState(() {
      _selectedYear = null;
    });
    _loadCourseLogData();
  }

  Future<void> _loadFilteredCourseLogData() async {
    if (_selectedYear == null) {
      _loadCourseLogData();
      return;
    }

    try {
      final phoneNumber = await _authLocalDataSource.getPhoneNumber();
      if (phoneNumber == null) return;

      final codeEtab = widget.school?.codeEtab ?? '';
      final codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.school?.codeUtilisateur ?? '';
      final codeUtilisateur = widget.etudiant != null ? widget.school?.codeUtilisateur : null;

      if (context.mounted) {
        context.read<CourseLogCubit>().loadFilteredCourseLogData(
          codeEtab: codeEtab,
          telephone: phoneNumber,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
          annee: _selectedYear!,
        );
      }
    } catch (e) {
      debugPrint('CahierTextePage - Error loading filtered course log data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: Builder( // Add Builder here
        builder: (context) { // Use the new context from Builder
          return BlocListener<CourseLogCubit, CourseLogState>(
            listener: (context, state) {
              if (state is CourseLogError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: CustomScrollView(
              slivers: [
                CustomAppBar(
                  pageSection: HeaderEnum.cahierDeTexte,
                  title: "CAHIER DE TEXTE",
                  isSearchBarVisible: _isSearchBarVisible,
                  etablissementUtilisateur: widget.school,
                  enfantDuTuteur: widget.etudiant,
                  onSearchTap: _toggleSearchBarVisibility,
                ),
                if (_isSearchBarVisible)
                  SliverPersistentHeader(
                    delegate: SearchBarSliver(
                      extentHeight: 60.0,
                      searchController: _searchController,
                      onSearchChanged: (query) => setState(() {}),
                      onDateFilterChanged: _onDateFilterChanged,
                      showYear: true,
                      hasActiveFilter: _selectedYear != null,
                      onClearDateFilter: _onClearDateFilter,
                      hintText: "Rechercher un cours...",
                    ),
                  ),
                BlocBuilder<CourseLogCubit, CourseLogState>(
                  builder: (context, state) {
                    if (state is CourseLogLoading) {
                      return SliverFillRemaining(
                        child: Center(
                          child: CustomSpinner(
                            size: 60.0,
                            strokeWidth: 5.0,
                          ),
                        ),
                      );
                    } else if (state is CourseLogLoaded) {
                      return _buildCourseLogList(state.courseLogData);
                    } else if (state is CourseLogError) {
                      return SliverFillRemaining(
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Erreur de chargement',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                state.message,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadCourseLogData,
                                child: const Text('Réessayer'),
                              ),
                            ],
                          ),
                        ),
                      );
                    } else {
                      return SliverFillRemaining(
                        child: Center(
                          child: EmptyMessage(
                            message: 'Aucun cours disponible',
                          ),
                        ),
                      );
                    }
                  },
                ),
              ],
            ),
          );
        }
      ), // Close Builder
    );
  }

  Widget _buildCourseLogList(List<List<CourseLogEntity>> courseLogData) {
    // Filter course logs based on search query
    final filteredData = courseLogData.map((dayLogs) {
      return dayLogs.where((courseLog) {
        if (_searchQuery.isEmpty) return true;

        final query = _searchQuery.toLowerCase();
        return courseLog.cours.toLowerCase().contains(query) ||
               courseLog.professeur.toLowerCase().contains(query) ||
               courseLog.classe.toLowerCase().contains(query) ||
               courseLog.semestre.toLowerCase().contains(query) ||
               courseLog.contenuSaisi.toLowerCase().contains(query);
      }).toList();
    }).where((dayLogs) => dayLogs.isNotEmpty).toList(); // Keep only days with filtered logs

    if (filteredData.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: EmptyMessage(
            message: _searchQuery.isEmpty
                ? 'Aucun cours disponible'
                : 'Aucun cours trouvé pour votre recherche',
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index < filteredData.length) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 4),
              child: CourseLogDayGroup(dayLogs: filteredData[index]),
            );
          }
          return const SizedBox.shrink();
        },
        childCount: filteredData.length,
      ),
    );
  }

}
