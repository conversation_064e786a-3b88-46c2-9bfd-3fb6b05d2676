import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/course_log_entity.dart';
import '../../domain/repositories/course_log_repository.dart';
import '../datasources/course_log_remote_datasource.dart';

/// Implementation of CourseLogRepository
class CourseLogRepositoryImpl implements CourseLogRepository {
  final CourseLogRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  CourseLogRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<List<CourseLogEntity>>>> getCourseLog({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteData = await remoteDataSource.getCourseLog(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );

        // Convert models to entities
        final List<List<CourseLogEntity>> result = remoteData.map((list) => list.map((model) => model.toEntity()).toList()).toList();
       
        return Right(result);
        
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('Unexpected error: $e'));
      }
    } else {
      return Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<List<CourseLogEntity>>>> getCourseLogAnnuel({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required int annee,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteData = await remoteDataSource.getCourseLogAnnuel(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
          annee: annee,
        );
       
        final List<List<CourseLogEntity>> result = remoteData.map((list) => list.map((model) => model.toEntity()).toList()).toList();

        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('Unexpected error: $e'));
      }
    } else {
      return Left(NetworkFailure('No internet connection'));
    }
  }
}