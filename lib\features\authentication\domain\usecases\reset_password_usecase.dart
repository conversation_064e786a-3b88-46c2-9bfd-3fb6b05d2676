import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/password_reset_entity.dart';
import '../repositories/auth_repository.dart';

/// Use case for resetting password
class ResetPasswordUseCase {
  final AuthRepository repository;

  ResetPasswordUseCase(this.repository);

  /// Execute the reset password use case
  ///
  /// [email] - The user's email address
  /// [pin] - The verification PIN
  /// [newPassword] - The new password
  /// [confirmPassword] - The password confirmation
  /// Returns [Either<Failure, PasswordResetResponseEntity>] - Success or failure result
  Future<Either<Failure, PasswordResetResponseEntity>> call(
    String email,
    String pin,
    String newPassword,
    String confirmPassword,
  ) async {
    return await repository.resetPassword(email, pin, newPassword, confirmPassword);
  }
}
