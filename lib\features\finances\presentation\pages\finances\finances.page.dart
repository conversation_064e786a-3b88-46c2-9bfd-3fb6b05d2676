import 'package:Kairos/core/theme/color_schemes.dart';
import 'package:Kairos/core/widgets/common/empty_message.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/enums/header_enums.dart';

import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/features/finances/presentation/pages/finances/finances_widgets/finance_item.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:Kairos/features/finances/presentation/bloc/finances_cubit.dart';
import 'package:Kairos/features/finances/presentation/bloc/finances_state.dart';
import 'package:Kairos/features/finances/domain/entities/frais_etudiant_entity.dart';
import 'package:Kairos/features/finances/domain/entities/finances_response_entity.dart'; 
import 'package:Kairos/features/finances/domain/entities/finances_unpaid_response_entity.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'package:intl/intl.dart';

class FinancesPage extends StatefulWidget{
  final int initialTab;
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;

  const FinancesPage({
    super.key,
    this.initialTab = 0,
    required this.school,
    this.etudiant,
  });

  @override
  State<FinancesPage> createState() => _FinancesPageState();
}



class _FinancesPageState extends State<FinancesPage> with TickerProviderStateMixin{
  late TabController _tabController;
  late bool _isSearchBarVisible = false;
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _searchAnimationController;
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;



  @override
  void initState(){
    super.initState();
    _tabController = TabController(initialIndex: widget.initialTab, length: 2, vsync: this);
    _searchController.addListener(_filterFinances);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );

    // Load finances data
    _loadFinancesData();
  }

  Future<void> _loadFinancesData() async {
    final phoneNumber = await _authLocalDataSource.getPhoneNumber();

    if (phoneNumber != null && mounted) {
      context.read<FinancesCubit>().loadFinancesData(
        codeEtab: widget.school.codeEtab,
        telephone: phoneNumber,
        codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur, // Use school's codeEtudiant if etudiant is null
        codeUtilisateur: widget.etudiant != null ? widget.school.codeUtilisateur: null,
      );
    }
  }

  @override
  void dispose(){
    _tabController.dispose();
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _filterFinances() {
    // This will be handled by BlocBuilder now
    // The filtering logic will be moved to the BlocBuilder
  }

  List<FraisEtudiantEntity> _filterFeesList(
    List<FraisEtudiantEntity> fees,
    String query,
    bool isPaidTab
  ) {
    return fees.where((fee) {
      // Text search filter only (date filtering is now handled server-side)
      bool matchesText = fee.intituleFrais.toLowerCase().contains(query);
      return matchesText;
    }).toList();
  }

  Widget _buildFeesTab(List<FraisEtudiantEntity> fees, bool isPaidTab, {FinancesResponseEntity? paidFeesResponse, FinancesUnpaidResponseEntity? unpaidFeesResponse}) {
    if (fees.isEmpty) {
      return Center(
        child: EmptyMessage(
          message: isPaidTab ? "Aucun frais payé trouvé" : "Aucun frais impayé trouvé",
        ),
      );
    }

    // Build the list of slivers for the CustomScrollView
    List<Widget> slivers = [];

    // Add summary text only for the paid tab if data is available
    if (isPaidTab && paidFeesResponse != null) {
      final montantEncaisser = paidFeesResponse.montantEncaisser ?? 0;
      final montantAEncaisser = paidFeesResponse.montantAEncaisser ?? 0;
      final anneeReference = paidFeesResponse.anneeReference ?? 'N/A'; // Handle null year

      final summaryText = "${NumberFormat("# ##0").format(montantEncaisser)} sur ${NumberFormat("# ##0").format(montantAEncaisser)} XOF encaissés pour $anneeReference";

      slivers.add(
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              summaryText,
              style: TextStyle(color: AppColorSchemes.errorRed, 
              fontSize: Theme.of(context).textTheme.titleMedium?.fontSize), // Adjust style as needed
            ),
          ),
        ),
      );
    }
    // Add summary text for unpaid tab if data is available
    if (!isPaidTab && unpaidFeesResponse != null) {
      final totalImpaye = unpaidFeesResponse.totalImpaye ?? 0;
      final summaryText = "TOTAL IMPAYÉ: ${NumberFormat('# ##0').format(totalImpaye)} XOF";
      slivers.add(
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              summaryText,
              style: TextStyle(color: AppColorSchemes.errorRed, 
              fontSize: Theme.of(context).textTheme.titleMedium?.fontSize), // Adjust style as needed
              ),
            ),
          ),
        );
    }

    // Add the list of finance items
    slivers.add(
      SliverPadding(
        padding: const EdgeInsets.only(top: 2.0, bottom: 2.0),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final fee = fees[index];
              return FinanceItem(
                intituleFrais: fee.intituleFrais,
                montantFrais: (fee.montantFrais ?? 0).toInt(),
                status: isPaidTab ? 'Quittance générée' : 'Aucune quittance générée',
                isPaid: isPaidTab,
                isObligatory: fee.indicateur?? false, // Default to true since we don't have this field
                montantEncaisseAjour: fee.montantEncaisseAjour?.toInt(),
                dateQuittance: fee.dateQuittance,
                numeroQuittance: fee.numeroQuittance,
              );
            },
            childCount: fees.length,
          ),
        ),
      ),
    );


    return CustomScrollView(
      slivers: slivers,
    );
  }



  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    // Trigger server-side filtering with date range
    _loadFinancesDataWithDateFilter();
  }

  // Method to clear date filters
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Load finances data without date filter (back to normal loading)
    _loadFinancesData();
  }

  // Load finances data with date filter (server-side filtering)
  Future<void> _loadFinancesDataWithDateFilter() async {
    if (_startDateFilter == null || _endDateFilter == null) {
      // If no date filter, use normal loading
      _loadFinancesData();
      return;
    }

    final phoneNumber = await _authLocalDataSource.getPhoneNumber();

    if (phoneNumber != null && mounted) {
      context.read<FinancesCubit>().loadFinancesDataWithDateFilter(
        codeEtab: widget.school.codeEtab,
        telephone: phoneNumber,
        dateDebut: _startDateFilter!,
        dateFin: _endDateFilter!,
        codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur, // Use school's codeEtudiant if etudiant is null
        codeUtilisateur: widget.etudiant != null ? widget.school.codeUtilisateur: null,
      );
    }
  }

  @override
  Widget build(BuildContext context){
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(pageSection: HeaderEnum.finances, title: "SITUATION FINANCIÈRE",
            isSearchBarVisible: _isSearchBarVisible,
            enfantDuTuteur: widget.etudiant,
            onSearchTap: () {
              setState(() {
                _isSearchBarVisible = !_isSearchBarVisible;
                if (!_isSearchBarVisible) {
                  _searchAnimationController.reverse();
                  _searchController.clear(); // Clear search when hidden
                  _filterFinances(); // Reset filter
                } else {
                  _searchAnimationController.forward();
                }
              });
            },
          ),
          SliverToBoxAdapter(
            child:
          TabBar(
            controller: _tabController,
            indicatorColor: Colors.amber,
            indicatorSize: TabBarIndicatorSize.tab,
            tabs: [
              Tab(text: "FRAIS IMPAYÉS"),
              Tab(text: "FRAIS PAYÉS"),
            ],
          ),
      ),
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
            pinned: true,
            delegate: SearchBarSliver(
            hintText: _tabController.index == 0? "Rechercher frais payés": _tabController.index == 1? "Rechercher frais impayés": "Rechercher ...",
            extentHeight: _isSearchBarVisible? 60.0 : 0.0,
            searchController: _searchController,
            onSearchChanged: (query) => _filterFinances(),
            onDateFilterChanged: _onDateFilterChanged,
            onClearDateFilter: _clearDateFilter,
            hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
          ),);
            },
          ),
        SliverFillRemaining(
          child: BlocBuilder<FinancesCubit, FinancesState>(
            builder: (context, state) {
              if (state is FinancesLoading) {
                return const Center(
                  child: CustomSpinner(
                    size: 60.0,
                    strokeWidth: 5.0,
                  ),
                );
              } else if (state is FinancesError) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Erreur lors du chargement',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadFinancesData,
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  ),
                );
              } else if (state is FinancesLoaded) {
                final query = _searchController.text.toLowerCase();
                final filteredPaidFees = _filterFeesList(state.paidFeesResponse?.fraisPayes ?? [], query, true);
                final filteredUnpaidFees = _filterFeesList(state.unpaidFeesResponse?.fraisImPayes ?? [], query, false);

                return TabBarView(
                  controller: _tabController,
                  children: [
                    // Unpaid fees tab
                    _buildFeesTab(filteredUnpaidFees, false, unpaidFeesResponse: state.unpaidFeesResponse),
                    // Paid fees tab
                    _buildFeesTab(filteredPaidFees, true, paidFeesResponse: state.paidFeesResponse),
                  ],
                );
              }

              return const SizedBox(); // Fallback in case of unexpected state
            },
          ),
        ),
        ],
      ),
    );
  }
}