import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/emploi_temps_entity.dart';

/// Abstract repository interface for schedule/timetable operations
abstract class EmploisDuTempsRepository {
  /// Get schedule/timetable data for a student
  /// 
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: User code (optional, used for PAR profile)
  Future<Either<Failure, List<List<EmploiTempsEntity>>>> getEmploisDuTemps({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered schedule/timetable data for a student with date range
  /// 
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: User code (optional, used for PAR profile)
  /// - [startDate]: Start date for filtering (optional)
  /// - [endDate]: End date for filtering (optional)
  Future<Either<Failure, List<List<EmploiTempsEntity>>>> getEmploisDuTempsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  });
}
