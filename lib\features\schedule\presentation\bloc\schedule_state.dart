﻿import 'package:equatable/equatable.dart';
import '../../domain/entities/emploi_temps_entity.dart';

/// Base schedule state
abstract class EmploiDuTempsState extends Equatable {
  const EmploiDuTempsState();

  @override
  List<Object?> get props => [];
}

/// Initial schedule state
class EmploiDuTempsInitial extends EmploiDuTempsState {
  const EmploiDuTempsInitial();
}

/// Loading state during schedule operations
class EmploiDuTempsLoading extends EmploiDuTempsState {
  const EmploiDuTempsLoading();
}

/// Schedule data loaded successfully
class EmploiDuTempsLoaded extends EmploiDuTempsState {
  final List<List<EmploiTempsEntity>> scheduleData;

  const EmploiDuTempsLoaded({required this.scheduleData});

  @override
  List<Object?> get props => [scheduleData];
}

/// Schedule error occurred
class EmploiDuTempsError extends EmploiDuTempsState {
  final String message;

  const EmploiDuTempsError(this.message);

  @override
  List<Object?> get props => [message];
}
