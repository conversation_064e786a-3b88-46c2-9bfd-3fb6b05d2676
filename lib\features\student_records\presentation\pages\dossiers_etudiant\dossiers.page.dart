import 'package:flutter/material.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Import flutter_bloc
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:Kairos/core/widgets/common/empty_message.widget.dart';
import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart'; // Import DossierEntity
import 'package:Kairos/features/student_records/presentation/pages/dossiers_etudiant/dossiers_widgets/dossier_item.widget.dart';
import 'package:Kairos/features/student_records/presentation/bloc/dossiers_cubit.dart'; // Import DossiersCubit
import 'package:Kairos/features/student_records/presentation/bloc/dossiers_state.dart'; // Import DossiersCubit
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart'; // Import EtablissementUtilisateur
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart'; // Import EnfantTuteurEntity
import 'package:Kairos/core/di/injection_container.dart' as di; // Import sl
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart'; // Import AuthLocalDataSource


  class DossiersPage extends StatefulWidget {
    // Add school and etudiant arguments to the constructor
    final EtablissementUtilisateur? school;
    final EnfantTuteurEntity? etudiant;
  
    const DossiersPage({super.key, this.school, this.etudiant});
  
    @override
    State<DossiersPage> createState() => _DossiersPageState();
  }
  
  class _DossiersPageState extends State<DossiersPage> with SingleTickerProviderStateMixin {
    // Change _isLoading to false initially, Cubit will handle loading state
    bool _isSearchBarVisible = false; // State to control search bar visibility
    late TextEditingController _searchController; // Controller for the search bar
    String? _yearFilter;
    final AuthLocalDataSource _authLocalDataSource  = di.sl<AuthLocalDataSource>();
  
    late AnimationController _searchAnimationController;
  
    @override
    void initState() {
      super.initState();
      _searchController = TextEditingController();
      _searchController.addListener(() {
        setState(() {
          // Trigger rebuild to re-evaluate filters
        });
      });
      _searchAnimationController = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 100),
      );
  
      // Fetch dossiers when the page is initialized
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _fetchDossiers();
      });

    }
  
    // Method to fetch dossiers using the Cubit, now supporting year filter
    Future<void> _fetchDossiers() async {
      try {
        final String codeEtab = widget.school!.codeEtab;
        final String codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.school!.codeUtilisateur;
        final String? codeUtilisateur = widget.etudiant != null ? widget.school!.codeUtilisateur : null;
        final String? telephone = await _authLocalDataSource.getPhoneNumber();

        debugPrint('codeEtab: $codeEtab');
        debugPrint('telephone: $telephone');
        debugPrint('codeEtudiant: $codeEtudiant');
        debugPrint('codeUtilisateur: $codeUtilisateur');
        debugPrint('yearFilter: $_yearFilter');

        if (mounted && codeEtab.isNotEmpty && telephone!.isNotEmpty && codeEtudiant.isNotEmpty) {
          if (_yearFilter != null && _yearFilter!.isNotEmpty) {
            context.read<DossiersCubit>().fetchDossiersByYear(
              codeEtab: codeEtab,
              telephone: telephone,
              codeEtudiant: codeEtudiant,
              annee: _yearFilter!,
              codeUtilisateur: codeUtilisateur,
            );
          } else {
            context.read<DossiersCubit>().fetchDossiers(
              codeEtab: codeEtab,
              telephone: telephone,
              codeEtudiant: codeEtudiant,
              codeUtilisateur: codeUtilisateur,
            );
          }
        } else {
          // Handle case where school or etudiant is null (e.g., show an error message)
          debugPrint("School or Etudiant data is missing.");
        }
      } catch (e) {
        debugPrint('Error fetching dossiers: $e');
      }
    }
  
  
    @override
    void dispose() {
      _searchController.dispose();
      _searchAnimationController.dispose();
      super.dispose();
    }
  
  
    List<DossierEntity> _getFilteredDossiers(List<DossierEntity> allDossiers) {
      final query = _searchController.text.toLowerCase();
      return allDossiers.where((dossier) {
        // Text search filter
        bool matchesText = dossier.objetSuivi.toLowerCase().contains(query) ||
               dossier.contenuSuivi.toLowerCase().contains(query) ||
               dossier.typeSuivi.toLowerCase().contains(query) ||
               dossier.membreAdministration.toLowerCase().contains(query) ||  
               dossier.dateSuivi.toLowerCase().contains(query); 

       
        return matchesText;
      }).toList();
    }
  
  
    void _onDateFilterChanged(Map<String, String> dateRange) {
      setState(() {
        _yearFilter = dateRange['endDate'];
      });
      _fetchDossiers(); // Call fetch dossiers with the new year filter
    }

    void _clearDateFilter() {
      setState(() {
        _yearFilter = null;
      });
      _fetchDossiers(); // Call fetch dossiers to clear the filter
    }
  
    void onSearchTap() {
      setState(() {
        _isSearchBarVisible = !_isSearchBarVisible;
        if (!_isSearchBarVisible) {
          _searchAnimationController.reverse();
          _searchController.clear(); // Clear search when hidden
          _yearFilter = null;
          // setState will trigger rebuild and re-evaluate filters
        } else {
          _searchAnimationController.forward();
        }
      });
    }
  
  
    @override
    Widget build(BuildContext context) {
      return Scaffold(
        body: CustomScrollView(
          slivers: [
            CustomAppBar(
                isSearchBarVisible: _isSearchBarVisible,
                onSearchTap: onSearchTap,
                pageSection: HeaderEnum.dossiers,
                etablissementUtilisateur: widget.school,
                enfantDuTuteur: widget.etudiant,
                title: "DOSSIERS ÉTUDIANT"),
            AnimatedBuilder(
              animation: _searchAnimationController,
              builder: (context, child) {
                return SliverPersistentHeader(
                  delegate: SearchBarSliver(
                    extentHeight: _searchAnimationController.value * 60.0,
                    searchController: _searchController,
                    showYear: true,
                    onSearchChanged: (query) {
                      setState(() {
                        final filteredDossiers = _getFilteredDossiers((context.read<DossiersCubit>().state as DossiersLoaded).dossiers);
                        debugPrint("all dossiers filtered -->$filteredDossiers");
                      });
                    },
                    onDateFilterChanged: _onDateFilterChanged,
                    onClearDateFilter: _clearDateFilter,
                    hasActiveFilter: _yearFilter != null,
                    hintText: "Rechercher un dossier...",
                  ),
                );
              },
            ),
            // Use BlocBuilder to react to DossiersCubit state changes
            BlocBuilder<DossiersCubit, DossiersState>(
              builder: (context, state) {
                if (state is DossiersLoading) {
                  return SliverFillRemaining(
                    child: Center(
                      child: CustomSpinner(
                        size: 60.0,
                        strokeWidth: 5.0,
                      ),
                    ),
                  );
                } else if (state is DossiersLoaded) {
                  final displayedDossiers = _getFilteredDossiers(state.dossiers);
                  return SliverFillRemaining(
                    child: displayedDossiers.isEmpty
                        ? Center(child: EmptyMessage(message: "Aucun dossier trouvé"))
                        : ListView.builder(
                            itemCount: displayedDossiers.length,
                            itemBuilder: (context, index) {
                              final dossier = displayedDossiers[index];
                              return DossierItem(
                                dossier: dossier,
                                school: widget.school, // Pass the school object
                                etudiant: widget.etudiant, // Pass the etudiant object
                              );
                            },
                          ),
                  );
                } else if (state is DossiersError) {
                  return SliverFillRemaining(
                    child: Center(
                      child: EmptyMessage(message: state.message),
                    ),
                  );
                }
                // For any other state (like AttachmentLoading, AttachmentLoaded, AttachmentError
                // which are now handled within DossiersLoaded), we should still display the list
                // if it was previously loaded.
                // However, since DossiersCubit now always emits DossiersLoaded with attachment status,
                // this 'else' block should ideally not be reached if the initial fetch was successful.
                // If it's the initial state before any fetch, show loading.
                return SliverFillRemaining(
                  child: Center(
                    child: CustomSpinner(
                      size: 60.0,
                      strokeWidth: 5.0,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      );
    }
  }
