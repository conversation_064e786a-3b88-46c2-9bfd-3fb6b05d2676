import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart'; // Import EtablissementUtilisateur
import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart'; // Import DossierEntity
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart'; // Import EnfantTuteurEntity
import 'package:Kairos/features/student_records/presentation/bloc/dossiers_cubit.dart'; // Add DossiersCubit import
import 'package:Kairos/features/student_records/presentation/pages/dossiers_etudiant/dossiers_widgets/dossier_details_dialog.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart'; // Import for date formatting
import "package:flutter_html/flutter_html.dart";

class DossierItem extends StatelessWidget {
  final DossierEntity dossier;
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant; // Add etudiant parameter

  const DossierItem({super.key, required this.dossier, this.school, this.etudiant});

  @override
  Widget build(BuildContext context) {
    // Format the date
    final formattedDate = DateFormat('dd MMM yyyy', 'fr').format(DateTime.parse(dossier.dateSuivi));

    // Get initials from typeSuivi
    String getInitials(String text) {
      if (text.isEmpty) return '';
      List<String> words = text.split(' ');
      if (words.length >= 2) {
        return words[0][0].toUpperCase() + words[1][0].toUpperCase();
      } else {
        return text[0].toUpperCase();
      }
    }
    final dossierColor = Color(int.parse(dossier.couleurTypeSuivi.replaceAll('#', '0xFF'))); // Added semicolon

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 7.0, vertical: 4.0),
      height: 75,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Theme.of(context).colorScheme.secondary, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ListTile(
        minVerticalPadding: 0,
        leading: CircleAvatar(
          radius: 30,
          // Use color from API response, fallback to primary color
          backgroundColor: dossierColor,
          child: Text(
            getInitials(dossier.typeSuivi), // Use typeSuivi for initials
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              dossier.typeSuivi, // Use typeSuivi
              style: TextStyle(fontSize: 10, color: dossierColor, fontWeight: FontWeight.bold), // Use the app's primary color
            ),
            Text(
              dossier.objetSuivi, // Use objetSuivi
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        subtitle: Text(
          HtmlParser.parseHTML(dossier.contenuSuivi).text,
          maxLines: 1, // Allow up to 2 lines
          style: TextStyle(fontSize: 10),
          overflow: TextOverflow.ellipsis, // Truncate with ellipsis if it exceeds 2 lines
        ), // Use contenuSuivi
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Conditionally show attachment icon based on indDocumentSuivi
                if (dossier.indDocumentSuivi)
                  const Icon(Icons.attach_file, size: 10, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  formattedDate, // Use formatted dateSuivi
                  style: const TextStyle(fontSize: 7, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          debugPrint('the user clicked on ${dossier.objetSuivi}'); // Use objetSuivi
          showDialog(
            context: context,
            builder: (dialogContext) => BlocProvider.value( // Use BlocProvider.value
              value: context.read<DossiersCubit>(), // Pass the existing cubit
              child: DossierDetailsDialogWidget(dossier: dossier, school: school, etudiant: etudiant), // Pass school and etudiant to dialog
            ),
          );
        },
      ),
    );
  }
}
