import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/password_reset_cubit.dart';
import 'package:Kairos/features/authentication/presentation/bloc/state/password_reset_state.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import "dart:math" as math;

class ReinitialiserMotDePasseWidget extends StatefulWidget {
  const ReinitialiserMotDePasseWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<ReinitialiserMotDePasseWidget> createState() => _ReinitialiserMotDePasseWidgetState();
}

class _ReinitialiserMotDePasseWidgetState extends State<ReinitialiserMotDePasseWidget> {
  final TextEditingController _pinController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _pinController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PasswordResetCubit, PasswordResetState>(
      listener: (context, state) {
        if (state is PasswordResetSuccess) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: state.message,
            ).getSnackBar(),
          );
          // Navigate to next page
          widget.pageController.nextPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        } else if (state is PasswordResetError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: state.message,
            ).getSnackBar(),
          );
        }
      },
      builder: (context, state) {
        _isLoading = state is PasswordResetLoading;
        
        return Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text(
              "RÉINITIALISATION DU MOT DE PASSE",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            Flexible(flex: 4, child: SvgPicture.asset("assets/images/logo_kairos.svg")),
            SizedBox(
              height: 20,
              width: 200,
              child: Divider(
                color: Theme.of(context).primaryColor,
                thickness: 5,
              ),
            ),
            const Spacer(),
            Flexible(flex: 8, child: SvgPicture.asset("assets/images/phone_auth.svg")),
            const Spacer(flex: 2),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Text(
                "Saisissez le code d'activation qui vous a été transmis et un nouveau mot de passe",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black.withValues(alpha: .6),
                ),
              ),
            ),
                    const Spacer(),
            Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                  children: [
                    // PIN Field
                    TextFormField(
                      controller: _pinController,
                      decoration: InputDecoration(
                        hintText: "Saisissez le PIN",
                        hintStyle: TextStyle(
                          color: Theme.of(context).colorScheme.secondary,
                          fontSize: 13,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: Theme.of(context).primaryColor),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: Theme.of(context).primaryColor),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 14,
                        ),
                        prefixIcon: Transform.rotate(angle: math.pi/2, child: Icon(Icons.vpn_key, color: Colors.black)),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(6),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "Veuillez saisir le code PIN";
                        }
                        if (value.length < 4) {
                          return "Le code PIN doit contenir au moins 4 chiffres";
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 15),
                    // New Password Field
                    TextFormField(
                      controller: _newPasswordController,
                      decoration: InputDecoration(
                        hintText: "Nouveau mot de passe",
                        hintStyle: TextStyle(
                          color: Theme.of(context).colorScheme.secondary,
                          fontSize: 13,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: Theme.of(context).primaryColor),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: Theme.of(context).primaryColor),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 14,
                        ),
                        prefixIcon: Icon(
                          Icons.lock,
                          color: Colors.black,
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureNewPassword ? Icons.visibility_off : Icons.visibility,
                            color: Colors.black,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscureNewPassword = !_obscureNewPassword;
                            });
                          },
                        ),
                      ),
                      obscureText: _obscureNewPassword,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "Veuillez saisir un nouveau mot de passe";
                        }
                        if (value.length < 6) {
                          return "Le mot de passe doit contenir au moins 6 caractères";
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 15),
                    // Confirm Password Field
                    TextFormField(
                      controller: _confirmPasswordController,
                      decoration: InputDecoration(
                        hintText: "Confirmation du mot de passe",
                        hintStyle: TextStyle(
                          color: Colors.black.withValues(alpha: 0.5),
                          fontSize: 13,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: Theme.of(context).primaryColor),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: Theme.of(context).primaryColor),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 14,
                        ),
                        prefixIcon: Icon(
                          Icons.lock,
                          color: Colors.black,
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
                            color: Colors.black,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscureConfirmPassword = !_obscureConfirmPassword;
                            });
                          },
                        ),
                      ),
                      obscureText: _obscureConfirmPassword,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "Veuillez confirmer votre mot de passe";
                        }
                        if (value != _newPasswordController.text) {
                          return "Les mots de passe ne correspondent pas";
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),
            Text(
              "Je n'ai pas reçu de CODE",
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).primaryColor,
                decoration: TextDecoration.underline,
                decorationColor: Theme.of(context).primaryColor,
              ),
            ),
            const Spacer(),
            FilledButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                fixedSize: WidgetStateProperty.all(const Size(335, 50)),
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              onPressed: _isLoading ? null : () {
                debugPrint('User clicked on Reset Password button');
                if (_formKey.currentState!.validate()) {
                  final email = context.read<PasswordResetCubit>().getStoredEmail();
                  if (email != null) {
                    // Trigger password reset via BLoC
                    context.read<PasswordResetCubit>().resetPassword(
                      email,
                      _pinController.text.trim(),
                      _newPasswordController.text.trim(),
                      _confirmPasswordController.text.trim(),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      CustomSnackbar(
                        message: "Email non trouvé. Veuillez recommencer.",
                      ).getSnackBar(),
                    );
                  }
                }
              },
              child: _isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      "RÉINITIALISER LE MOT DE PASSE",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
            
            const Spacer(),
          ],
        );
      },
    );
  }
}
