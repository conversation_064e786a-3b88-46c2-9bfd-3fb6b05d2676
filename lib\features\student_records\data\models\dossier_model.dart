import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart'; // Import the entity

// Data model for a single dossier item received from the API
class DossierModel {
  final String typeSuivi;
  final String couleurTypeSuivi;
  final String dateSuivi;
  final String objetSuivi;
  final String contenuSuivi;
  final String membreAdministration;
  final bool indDocumentSuivi;
  final int idObjet;

  DossierModel({
    required this.typeSuivi,
    required this.couleurTypeSuivi,
    required this.dateSuivi,
    required this.objetSuivi,
    required this.contenuSuivi,
    required this.membreAdministration,
    required this.indDocumentSuivi,
    required this.idObjet,
  });

  // Factory constructor to create a DossierModel from JSON
  factory DossierModel.fromJson(Map<String, dynamic> json) {
    return DossierModel(
      typeSuivi: json['typeSuivi'] as String,
      couleurTypeSuivi: json['couleurTypeSuivi'] as String,
      dateSuivi: json['dateSuivi'] as String,
      objetSuivi: json['objetSuivi'] as String,
      contenuSuivi: json['contenuSuivi'] as String,
      membreAdministration: json['membreAdministration'] as String,
      indDocumentSuivi: json['indDocumentSuivi'] as bool,
      idObjet: json['idObjet'] as int,
    );
  }

  // Method to convert DossierModel to JSON (optional, but good practice)
  Map<String, dynamic> toJson() {
    return {
      'typeSuivi': typeSuivi,
      'couleurTypeSuivi': couleurTypeSuivi,
      'dateSuivi': dateSuivi,
      'objetSuivi': objetSuivi,
      'contenuSuivi': contenuSuivi,
      'membreAdministration': membreAdministration,
      'indDocumentSuivi': indDocumentSuivi,
      'idObjet': idObjet,
    };
  }

  // Method to convert DossierModel to DossierEntity
  DossierEntity toEntity() {
    return DossierEntity(
      typeSuivi: typeSuivi,
      couleurTypeSuivi: couleurTypeSuivi,
      dateSuivi: dateSuivi,
      objetSuivi: objetSuivi,
      contenuSuivi: contenuSuivi,
      membreAdministration: membreAdministration,
      indDocumentSuivi: indDocumentSuivi,
      idObjet: idObjet,
    );
  }
}