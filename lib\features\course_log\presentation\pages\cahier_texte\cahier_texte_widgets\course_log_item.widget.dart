import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/features/course_log/domain/entities/course_log_entity.dart';
// import 'package:Kairos/features/course_log/presentation/bloc/course_log_cubit.dart';
import 'package:Kairos/features/course_log/presentation/pages/cahier_texte/cahier_texte_widgets/course_log_details_dialog.widget.dart';


class CourseLogItem extends StatelessWidget {
  final CourseLogEntity courseLog;

  const CourseLogItem({super.key, required this.courseLog});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            // Provide the existing CourseLogCubit to the dialog's context
            // return BlocProvider.value(
            //   value: context.read<CourseLogCubit>(),
            //   child: CourseLogDetailsDialog(courseLog: courseLog),
            // );
            return CourseLogDetailsDialog(courseLog: courseLog);
          },
        );
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 1.0),
        color: Colors.white,
        shape: RoundedRectangleBorder(
          side: BorderSide(color: Theme.of(context).colorScheme.secondary, width: 1),
          borderRadius: BorderRadius.circular(0)
        ),
        child: Padding(
          padding: const EdgeInsets.all(7.0),
          child: Column( // Use Column as the main layout widget
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row( // Row 1: Time, Professor, Duration
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded( // Use Expanded for the time and professor text
                    child: Text(
                      '${courseLog.heureDebutPrevu} - ${courseLog.heureFinPrevu} | ${courseLog.professeur}',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Colors.black.withValues(alpha: 0.8),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Row( // Row for clock icon and duration
                    children: [
                      const Icon(Icons.access_time, size: 14, color: Colors.black), // Clock icon
                      const SizedBox(width: 4),
                      Text(
                        courseLog.dureeSaisi,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Colors.black.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 4), // Spacing between rows
              Text( // Row 2: Course name
                courseLog.cours,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 4), // Spacing between rows
              Row( // Row 3: Author, Date, Class, Semester
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded( // Use Expanded for author and date
                    child: Text(
                      'Enregistré par ${courseLog.auteurSaisi} | ${_formatDate(courseLog.dateCours)}',
                      style: TextStyle(
                        fontSize: 9,
                        fontWeight: FontWeight.w400,
                        color: Colors.black.withValues(alpha: 0.5),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text( // Class and Semester
                    '${courseLog.classe} | ${courseLog.semestre}',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: Colors.black.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to format date as dd/mm
  String _formatDate(String dateString) {
    try {
      final dateParts = dateString.split('-');
      if (dateParts.length >= 3) {
        return '${dateParts[2]}/${dateParts[1]}';
      }
      return dateString;
    } catch (e) {
      return dateString;
    }
  }
}
