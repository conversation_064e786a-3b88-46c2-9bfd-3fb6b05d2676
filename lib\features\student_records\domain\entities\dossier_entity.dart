import 'package:equatable/equatable.dart';

// Domain entity for a dossier item
class DossierEntity extends Equatable {
  final String typeSuivi;
  final String couleurTypeSuivi;
  final String dateSuivi;
  final String objetSuivi;
  final String contenuSuivi;
  final String membreAdministration;
  final bool indDocumentSuivi;
  final int idObjet;

  const DossierEntity({
    required this.typeSuivi,
    required this.couleurTypeSuivi,
    required this.dateSuivi,
    required this.objetSuivi,
    required this.contenuSuivi,
    required this.membreAdministration,
    required this.indDocumentSuivi,
    required this.idObjet,
  });

  @override
  List<Object?> get props => [
        typeSuivi,
        couleurTypeSuivi,
        dateSuivi,
        objetSuivi,
        contenuSuivi,
        membreAdministration,
        indDocumentSuivi,
        idObjet,
      ];
}