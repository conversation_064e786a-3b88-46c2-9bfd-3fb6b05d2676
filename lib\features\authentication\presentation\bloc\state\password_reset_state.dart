import 'package:equatable/equatable.dart';

/// Base password reset state
abstract class PasswordResetState extends Equatable {
  const PasswordResetState();
  
  @override
  List<Object?> get props => [];
}

/// Initial password reset state
class PasswordResetInitial extends PasswordResetState {
  const PasswordResetInitial();
}

/// Loading state during password reset operations
class PasswordResetLoading extends PasswordResetState {
  const PasswordResetLoading();
}

/// Email sent successfully state
class PasswordResetEmailSent extends PasswordResetState {
  final String email;
  final String message;

  const PasswordResetEmailSent({
    required this.email,
    required this.message,
  });

  @override
  List<Object?> get props => [email, message];
}

/// Password reset completed successfully state
class PasswordResetSuccess extends PasswordResetState {
  final String message;

  const PasswordResetSuccess({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

/// Error state during password reset operations
class PasswordResetError extends PasswordResetState {
  final String message;
  final int? returnCode;

  const PasswordResetError({
    required this.message,
    this.returnCode,
  });

  @override
  List<Object?> get props => [message, returnCode];
}
