import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/course_log_entity.dart';
import '../repositories/course_log_repository.dart';

/// Use case for getting course log data
class GetCourseLogUseCase implements UseCase<List<List<CourseLogEntity>>, GetCourseLogParams> {
  final CourseLogRepository repository;

  GetCourseLogUseCase(this.repository);

  @override
  Future<Either<Failure, List<List<CourseLogEntity>>>> call(GetCourseLogParams params) async {
    return await repository.getCourseLog(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

/// Parameters for GetCourseLogUseCase
class GetCourseLogParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  GetCourseLogParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });
}