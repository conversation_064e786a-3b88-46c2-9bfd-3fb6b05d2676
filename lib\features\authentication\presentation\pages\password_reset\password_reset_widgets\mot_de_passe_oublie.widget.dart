import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/features/authentication/presentation/bloc/state/password_reset_state.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/password_reset_cubit.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';



class MotDePasseOublieWidget extends StatefulWidget {
  const MotDePasseOublieWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<MotDePasseOublieWidget> createState() => _MotDePasseOublieWidgetState();
}

class _MotDePasseOublieWidgetState extends State<MotDePasseOublieWidget> {
  final TextEditingController _emailController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PasswordResetCubit, PasswordResetState>(
      listener: (context, state) {
        if (state is PasswordResetEmailSent) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Code de réinitialisation envoyé à: ${state.email}",
            ).getSnackBar(),
          );
          // Navigate to next page
          widget.pageController.nextPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        } else if (state is PasswordResetError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: state.message,
            ).getSnackBar(),
          );
        }
      },
      builder: (context, state) {
        _isLoading = state is PasswordResetLoading;
        
        return Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text(
              "MOT DE PASSE OUBLIÉ",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            SvgPicture.asset("assets/images/logo_kairos.svg"),
            SizedBox(
              height: 20,
              width: 200,
              child: Divider(
                color: Theme.of(context).primaryColor,
                thickness: 5,
              ),
            ),
            const Spacer(),
            Flexible(flex: 8, child: SvgPicture.asset("assets/images/phone_auth.svg")),
            const Spacer(flex: 2),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Text(
                "Saisissez votre adresse email pour recevoir le code de réinitialisation",
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 20),
            Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    hintText: "<EMAIL>",
                    hintStyle: TextStyle(color: Colors.grey.shade400),
                    border: const OutlineInputBorder(),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 14,
                    ),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Veuillez saisir votre adresse email";
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                      return "Veuillez saisir une adresse email valide";
                    }
                    return null;
                  },
                ),
              ),
            ),
            const SizedBox(height: 30),
            FilledButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                fixedSize: WidgetStateProperty.all(const Size(300, 50)),
              ),
              onPressed: _isLoading ? null : () {
                debugPrint('User clicked on Continue button');
                if (_formKey.currentState!.validate()) {
                  // Trigger password reset email sending via BLoC
                  context.read<PasswordResetCubit>().sendPasswordResetEmail(
                    _emailController.text.trim(),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    CustomSnackbar(
                      message: "Veuillez saisir une adresse email valide",
                    ).getSnackBar(),
                  );
                }
              },
              child: _isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text("SUIVANT"),
            ),
            const SizedBox(height: 20),
            const Spacer(),
          ],
        );
      },
    );
  }
}
