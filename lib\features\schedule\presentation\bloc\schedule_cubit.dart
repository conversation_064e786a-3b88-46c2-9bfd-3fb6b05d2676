﻿import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_emplois_du_temps_usecase.dart';
import '../../domain/usecases/get_emplois_du_temps_filtres_usecase.dart';
import 'schedule_state.dart';

/// EmploiDuTemps Cubit for managing schedule state
class EmploiDuTempsCubit extends Cubit<EmploiDuTempsState> {
  final GetEmploisDuTempsUseCase getEmploisDuTempsUseCase;
  final GetEmploisDuTempsFiltresUseCase getEmploisDuTempsFiltresUseCase;

  EmploiDuTempsCubit({
    required this.getEmploisDuTempsUseCase,
    required this.getEmploisDuTempsFiltresUseCase,
  }) : super(const EmploiDuTempsInitial());

  /// Load schedule data
  Future<void> loadScheduleData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const EmploiDuTempsLoading());

    try {
      debugPrint('EmploiDuTempsCubit: Loading schedule data for student: $codeEtudiant');

      final result = await getEmploisDuTempsUseCase(GetEmploisDuTempsParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ));

      result.fold(
        (failure) {
          debugPrint('EmploiDuTempsCubit: Failed to load schedule data: ${failure.message}');
          emit(EmploiDuTempsError(failure.message));
        },
        (scheduleData) {
          debugPrint('EmploiDuTempsCubit: Successfully loaded ${scheduleData.length} days of schedule data');
          emit(EmploiDuTempsLoaded(scheduleData: scheduleData));
        },
      );
    } catch (e) {
      debugPrint('EmploiDuTempsCubit: Unexpected error: $e');
      emit(EmploiDuTempsError('Unexpected error: $e'));
    }
  }

  /// Load filtered schedule data
  Future<void> loadFilteredScheduleData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  }) async {
    emit(const EmploiDuTempsLoading());

    try {
      debugPrint('EmploiDuTempsCubit: Loading filtered schedule data for student: $codeEtudiant, dates: $startDate to $endDate');

      final result = await getEmploisDuTempsFiltresUseCase(GetEmploisDuTempsFiltresParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
        startDate: startDate,
        endDate: endDate,
      ));

      result.fold(
        (failure) {
          debugPrint('EmploiDuTempsCubit: Failed to load filtered schedule data: ${failure.message}');
          emit(EmploiDuTempsError(failure.message));
        },
        (scheduleData) {
          debugPrint('EmploiDuTempsCubit: Successfully loaded ${scheduleData.length} days of filtered schedule data');
          emit(EmploiDuTempsLoaded(scheduleData: scheduleData));
        },
      );
    } catch (e) {
      debugPrint('EmploiDuTempsCubit: Unexpected error during filtering: $e');
      emit(EmploiDuTempsError('Unexpected error: $e'));
    }
  }

  /// Refresh schedule data
  Future<void> refresh({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    await loadScheduleData(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );
  }
}
