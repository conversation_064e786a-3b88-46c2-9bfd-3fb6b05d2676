import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart'; // Assuming qr_flutter is available
import 'package:flutter_bloc/flutter_bloc.dart'; // Import for BlocBuilder
import 'package:Kairos/features/profile/domain/entities/profile_entity.dart'; // Import for ProfileEntity
import 'package:Kairos/features/profile/presentation/bloc/profile_cubit.dart'; // Import for ProfileCubit
import 'package:Kairos/features/profile/presentation/bloc/profile_state.dart'; // Import for ProfileState
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/features/profile/data/profile_type.enum.dart'; // For ProfileType enum

class CarteVirtuellePage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant; // Made optional

  const CarteVirtuellePage({
    super.key,
    required this.school,
    this.etudiant, // Now optional
  });

  @override
  State<CarteVirtuellePage> createState() => _CarteVirtuellePageState();
}

class _CarteVirtuellePageState extends State<CarteVirtuellePage> {
  @override
  Widget build(BuildContext context) {
    const double heroHeight = 200.0;

    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        ProfileEntity? displayProfile;
        bool isLoading = false;

        if (state is ProfileLoading) {
          isLoading = true;
        } else if (state is ProfileLoaded) {
          displayProfile = state.profile; // Corrected from profileEntity to profile
        } else if (state is ProfileError) {
          // Handle error state, maybe show a message
          return Scaffold(
            appBar: AppBar(
              title: const Text('Ma carte virtuelle'),
              centerTitle: false,
              foregroundColor: Colors.white,
              backgroundColor: Colors.transparent,
              elevation: 0,
            ),
            body: Center(
              child: Text('Erreur de chargement du profil: ${state.message}'),
            ),
          );
        }

        // If an optional etudiant is provided, prioritize it over the loaded profile
        // This allows the page to be used with pre-existing student data if needed.
        // Cast EnfantTuteurEntity to ProfileEntity if it's not already a subtype
        if (widget.etudiant != null) {
          displayProfile = widget.etudiant;
        } else if (displayProfile == null && widget.etudiant != null) {
          // If profile is not loaded from cubit but optional etudiant is provided, use it
          displayProfile = widget.etudiant; // EnfantTuteurEntity now extends ProfileEntity, so direct assignment is fine
        }

        if (isLoading) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (displayProfile == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Ma carte virtuelle'),
              centerTitle: false,
              foregroundColor: Colors.white,
              backgroundColor: Colors.transparent,
              elevation: 0,
            ),
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: const Center(
                child: Text('Impossible de charger les informations du profil.'),
              ),
            ),
          );
        }

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('Ma carte virtuelle'),
        centerTitle: false,
        foregroundColor: Colors.white,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: heroHeight + 150, // Extended height to accommodate floating card
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // Hero background image
                  Hero(
                    tag: "hero_profile",
                    transitionOnUserGestures: true,
                    child: Image.asset(
                      "assets/images/header_dashboard.png",
                      width: MediaQuery.of(context).size.width,
                      fit: BoxFit.cover,
                    ),
                  ),
                  // Floating profile card positioned over hero section
                  Positioned(
                    top: 83, // 83px from screen top
                    left: (MediaQuery.of(context).size.width - 302) / 2, // Centered horizontally
                    child: _buildFloatingProfileCard(displayProfile),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 0, left: 16, right: 16),
              child: _buildQrCodeSection(displayProfile, widget.school),
            ),
          ],
        ),
      ),
    );
  });
  }

  // Helper methods for accessing profile data
  String _getPhoto(ProfileEntity? profile) {
    if (profile is EnfantTuteurEntity) {
      return profile.photo;
    }
    return profile?.photo ?? '';
  }

  String _getProfileTypeCode(ProfileEntity? profile) {
    if (profile is EnfantTuteurEntity) {
      return ProfileType.etudiant.code;
    }
    return profile?.profil ?? ProfileType.unknown.code;
  }

  Widget _buildFloatingProfileCard(ProfileEntity? profile) {
    return Stack(
      children: [
        Container(
          width: 302,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.black.withOpacity(0.2)),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.25),
                offset: const Offset(0, 4),
                blurRadius: 4,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              const SizedBox(height: 206),
              const SizedBox(height: 13),
              Text(
                profile?.fullName ?? 'N/A', // Using fullName directly
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w900,
                  color: Colors.black,
                  height: 1.21,
                ),
              ),
              const SizedBox(height: 13),
             Text("Matricule: ${profile?.codeUtilisateur ?? 'N/A'}"),
             Text("Programme:"),
             Text("Niveau:"),
             Text("Classe:"),
              const SizedBox(height: 7),
            ],
          ),
        ),
        Positioned(
          left: 50.0,
          top: 10.0,
          child: _buildProfileImage(profile),
        ),
      ],
    );
  }

  Widget _buildProfileImage(ProfileEntity? profile) {
    ImageProvider backgroundImage;
    final String photoData = _getPhoto(profile);

    if (photoData.isNotEmpty) {
      try {
        backgroundImage = MemoryImage(
          base64Decode(photoData.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
        );
      } catch (_) {
        backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
      }
    } else {
      backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
    }

    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 200,
        backgroundImage: backgroundImage,
      ),
    );
  }

  Widget _buildQrCodeSection(ProfileEntity? profile, EtablissementUtilisateur school) {
    final String fullName = profile?.fullName ?? 'N/A'; // Using fullName directly
    final String schoolName = school.libelleEtab;
    final String schoolCode = school.codeEtab;
    final String profileTypeCode = _getProfileTypeCode(profile);

    final String qrData = jsonEncode({
      'fullName': fullName,
      'schoolName': schoolName,
      'schoolCode': schoolCode,
      'profileType': profileTypeCode,
    });

    return Center(
      child: Column(
        children: [
          const SizedBox(height: 100),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  offset: const Offset(0, 4),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: QrImageView(
              data: qrData,
              version: QrVersions.auto,
              size: 200.0,
              gapless: false
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

