import 'package:equatable/equatable.dart';

class DeleteAccountRequest extends Equatable {
  final String numeroTelephone;
  final String marqueTelephone;
  final String modelTelephone;
  final String imeiTelephone;
  final String numeroSerie;

  const DeleteAccountRequest({
    required this.numeroTelephone,
    required this.marqueTelephone,
    required this.modelTelephone,
    required this.imeiTelephone,
    required this.numeroSerie,
  });

  Map<String, dynamic> toJson() {
    return {
      'numeroTelephone': numeroTelephone,
      'marqueTelephone': marqueTelephone,
      'modelTelephone': modelTelephone,
      'imeiTelephone': imeiTelephone,
      'numeroSerie': numeroSerie,
    };
  }

  @override
  List<Object?> get props => [
        numeroTelephone,
        marqueTelephone,
        modelTelephone,
        imeiTelephone,
        numeroSerie,
      ];
}