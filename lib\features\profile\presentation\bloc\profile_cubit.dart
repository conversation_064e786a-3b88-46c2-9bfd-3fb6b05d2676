﻿import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/error/failures.dart';
import '../../domain/usecases/get_profile_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/delete_account_usecase.dart'; // New import
import '../../../authentication/data/models/deconnexion_request.dart';
import '../../../authentication/data/models/delete_account_request.dart'; // New import
import 'profile_state.dart';

/// Profile Cubit for managing profile state
class ProfileCubit extends Cubit<ProfileState> {
  final GetProfileUseCase _getProfileUseCase;
  final LogoutUseCase _logoutUseCase;
  final DeleteAccountUseCase _deleteAccountUseCase; // New use case

  ProfileCubit({
    required GetProfileUseCase getProfileUseCase,
    required LogoutUseCase logoutUseCase,
    required DeleteAccountUseCase deleteAccountUseCase, // New dependency
  }) : _getProfileUseCase = getProfileUseCase,
       _logoutUseCase = logoutUseCase,
       _deleteAccountUseCase = deleteAccountUseCase, // Initialize new use case
       super(const ProfileInitial());
  
  /// Load profile data for a specific user using codeUtilisateur
  Future<void> loadProfileData(String codeUtilisateur) async { // Accept codeUtilisateur
    emit(const ProfileLoading());

    // Pass the codeUtilisateur to the use case
    final failureOrProfile = await _getProfileUseCase(codeUtilisateur);

    failureOrProfile.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (profile) {
        if (profile != null) {
          emit(ProfileLoaded(profile: profile));
        } else {
          emit(const ProfileNotFound());
        }
      },
    );
  }
  
  /// Refresh profile data for a specific user using codeUtilisateur
  Future<void> refresh(String codeUtilisateur) async { // Accept codeUtilisateur
    // Pass the codeUtilisateur to loadProfileData
    await loadProfileData(codeUtilisateur);
  }

  /// Logout user
  Future<void> logout(DeconnexionRequest request) async {
    emit(const LogoutLoading());

    final failureOrSuccess = await _logoutUseCase(request);

    failureOrSuccess.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (_) => emit(const LogoutSuccess()),
    );
  }

  /// Delete user account
  Future<void> deleteAccount(DeleteAccountRequest request) async {
    emit(const ProfileDeleting());

    final failureOrSuccess = await _deleteAccountUseCase(request);

    failureOrSuccess.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (_) => emit(const ProfileDeleted()),
    );
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return 'Erreur de connexion. Vérifiez votre connexion internet.';
    } else if (failure is CacheFailure) {
      return 'Erreur de stockage local.';
    } else {
      return 'Une erreur inattendue s\'est produite.';
    }
  }
}
