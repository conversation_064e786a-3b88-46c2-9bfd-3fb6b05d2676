import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'password_reset_widgets/mot_de_passe_oublie.widget.dart';
import 'password_reset_widgets/reinitialiser_mot_de_passe.widget.dart';
import 'password_reset_widgets/mot_de_passe_reinitialise.widget.dart';

class PasswordResetPage extends StatefulWidget {
  const PasswordResetPage({super.key});

  @override
  State<PasswordResetPage> createState() => _PasswordResetPageState();
}

class _PasswordResetPageState extends State<PasswordResetPage> {
  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Si<PERSON><PERSON><PERSON>(height: 40),
                Expanded(

                  child: <PERSON><PERSON><PERSON><PERSON>(
                    physics: const AlwaysScrollableScrollPhysics(),
                    controller: _pageController,
                    children: [
                      MotDePasseOublieWidget(pageController: _pageController),
                      ReinitialiserMotDePasseWidget(pageController: _pageController),
                      MotDePasseReinitialiseWidget(pageController: _pageController),
                    ],
                  ),
                ),
                // Bottom decoration similar to accueil page
               Flexible(flex: 0, child: SvgPicture.asset("assets/images/logo_footer.svg")),
               SizedBox(height: 10)
              ],
            )
        
    );
  }
}
