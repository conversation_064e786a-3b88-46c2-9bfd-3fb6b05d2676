import 'package:equatable/equatable.dart';

/// Domain entity for a dossier attachment file
class DossierAttachmentEntity extends Equatable {
  final String file; // base64 encoded file content
  final String type; // MIME type (e.g., "application/pdf")
  final String returnCode; // API return code (e.g., "SUCCESS")

  const DossierAttachmentEntity({
    required this.file,
    required this.type,
    required this.returnCode,
  });

  @override
  List<Object?> get props => [file, type, returnCode];
}
