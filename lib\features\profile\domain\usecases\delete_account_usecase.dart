import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../authentication/data/models/delete_account_request.dart';
import '../repositories/profile_repository.dart';

class DeleteAccountUseCase implements UseCase<void, DeleteAccountRequest> {
  final ProfileRepository repository;

  DeleteAccountUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteAccountRequest request) async {
    return await repository.deleteAccount(request);
  }
}