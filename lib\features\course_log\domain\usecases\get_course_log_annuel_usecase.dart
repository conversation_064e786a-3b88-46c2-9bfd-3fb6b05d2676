import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/course_log_entity.dart';
import '../repositories/course_log_repository.dart';

/// Use case for getting annual course log data with filtering
class GetCourseLogAnnuelUseCase implements UseCase<List<List<CourseLogEntity>>, GetCourseLogAnnuelParams> {
  final CourseLogRepository repository;

  GetCourseLogAnnuelUseCase(this.repository);

  @override
  Future<Either<Failure, List<List<CourseLogEntity>>>> call(GetCourseLogAnnuelParams params) async {
    return await repository.getCourseLogAnnuel(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
      annee: params.annee,
    );
  }
}

/// Parameters for GetCourseLogAnnuelUseCase
class GetCourseLogAnnuelParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;
  final int annee;

  GetCourseLogAnnuelParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
    required this.annee,
  });
}