import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart';
import 'package:Kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';
import 'package:equatable/equatable.dart';


abstract class DossiersState extends Equatable {
  const DossiersState();

  @override
  List<Object> get props => [];
}

class DossiersInitial extends DossiersState {}

class DossiersLoading extends DossiersState {}

class DossiersError extends DossiersState {
  final String message;

  const DossiersError(this.message);

  @override
  List<Object> get props => [message];
}

// Define an enum for attachment status to manage its state within DossiersLoaded
enum AttachmentStatus {
  initial, // No attachment operation in progress
  loading, // Attachment is being fetched/processed
  loaded,  // Attachment has been successfully loaded
  error,   // An error occurred during attachment operation
}

class DossiersLoaded extends DossiersState {
  final List<DossierEntity> dossiers;
  // Add attachment-related properties to DossiersLoaded state
  final AttachmentStatus attachmentStatus;
  final String? attachmentErrorMessage;

  const DossiersLoaded(
    this.dossiers, {
    this.attachmentStatus = AttachmentStatus.initial,
    this.attachmentErrorMessage,
  });

  @override
  List<Object> get props => [dossiers, attachmentStatus, attachmentErrorMessage ?? ''];

  // Helper method to create a new DossiersLoaded state with updated attachment status
  DossiersLoaded copyWith({
    List<DossierEntity>? dossiers,
    AttachmentStatus? attachmentStatus,
    String? attachmentErrorMessage,
  }) {
    return DossiersLoaded(
      dossiers ?? this.dossiers,
      attachmentStatus: attachmentStatus ?? this.attachmentStatus,
      attachmentErrorMessage: attachmentErrorMessage ?? this.attachmentErrorMessage,
    );
  }
}