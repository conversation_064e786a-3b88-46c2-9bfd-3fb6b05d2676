import '../../domain/entities/course_log_entity.dart';

/// Data model for course log (cahier texte) items
class CourseLogModel {
  final String dateCours;
  final String heureDebutPrevu;
  final String heureFinPrevu;
  final String heureDebutSaisi;
  final String heureFinSaisi;
  final String dureeSaisi;
  final String cours;
  final String professeur;
  final String semestre;
  final String auteurSaisi;
  final String classe;
  final String contenuSaisi;
  final int idObject;
  final String matriculeProfesseur;
  final int idPieceJointe;

  CourseLogModel({
    required this.dateCours,
    required this.heureDebutPrevu,
    required this.heureFinPrevu,
    required this.heureDebutSaisi,
    required this.heureFinSaisi,
    required this.dureeSaisi,
    required this.cours,
    required this.professeur,
    required this.semestre,
    required this.auteurSaisi,
    required this.classe,
    required this.contenuSaisi,
    required this.idObject,
    required this.matriculeProfesseur,
    required this.idPieceJointe,
  });

  factory CourseLogModel.fromJson(Map<String, dynamic> json) {
    return CourseLogModel(
      dateCours: json['dateCours'] ?? '',
      heureDebutPrevu: json['heureDebutPrevu'] ?? '',
      heureFinPrevu: json['heureFinPrevu'] ?? '',
      heureDebutSaisi: json['heureDebutSaisi'] ?? '',
      heureFinSaisi: json['heureFinSaisi'] ?? '',
      dureeSaisi: json['dureeSaisi'] ?? '',
      cours: json['cours'] ?? '',
      professeur: json['professeur'] ?? '',
      semestre: json['semestre'] ?? '',
      auteurSaisi: json['auteurSaisi'] ?? '',
      classe: json['classe'] ?? '',
      contenuSaisi: json['contenuSaisi'] ?? '',
      idObject: json['idObject'] ?? 0,
      matriculeProfesseur: json['matriculeProfesseur'] ?? '',
      idPieceJointe: json['idPieceJointe'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dateCours': dateCours,
      'heureDebutPrevu': heureDebutPrevu,
      'heureFinPrevu': heureFinPrevu,
      'heureDebutSaisi': heureDebutSaisi,
      'heureFinSaisi': heureFinSaisi,
      'dureeSaisi': dureeSaisi,
      'cours': cours,
      'professeur': professeur,
      'semestre': semestre,
      'auteurSaisi': auteurSaisi,
      'classe': classe,
      'contenuSaisi': contenuSaisi,
      'idObject': idObject,
      'matriculeProfesseur': matriculeProfesseur,
      'idPieceJointe': idPieceJointe,
    };
  }

  /// Converts CourseLogModel to CourseLogEntity
  CourseLogEntity toEntity() {
    return CourseLogEntity(
      dateCours: dateCours,
      heureDebutPrevu: heureDebutPrevu,
      heureFinPrevu: heureFinPrevu,
      heureDebutSaisi: heureDebutSaisi,
      heureFinSaisi: heureFinSaisi,
      dureeSaisi: dureeSaisi,
      cours: cours,
      professeur: professeur,
      semestre: semestre,
      auteurSaisi: auteurSaisi,
      classe: classe,
      contenuSaisi: contenuSaisi,
      idObject: idObject,
      matriculeProfesseur: matriculeProfesseur,
      idPieceJointe: idPieceJointe,
    );
  }
}