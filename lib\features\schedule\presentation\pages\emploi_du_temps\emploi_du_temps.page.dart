import 'package:Kairos/features/schedule/domain/entities/emploi_temps_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/core/widgets/common/empty_message.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/core/di/injection_container.dart';
import '../../bloc/schedule_cubit.dart';
import '../../bloc/schedule_state.dart';
import 'emploi_temps_item.widget.dart';

class EmploiDuTempsPage extends StatefulWidget {
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const EmploiDuTempsPage({
    super.key,
    this.school,
    this.etudiant,
  });

  @override
  State<EmploiDuTempsPage> createState() => _EmploiDuTempsPageState();
}

class _EmploiDuTempsPageState extends State<EmploiDuTempsPage> with TickerProviderStateMixin {
  // Helper method to get weekday abbreviation in French
  String _getWeekdayAbbr(DateTime date) {
    const weekdays = {
      1: 'LUN',
      2: 'MAR',
      3: 'MER',
      4: 'JEU',
      5: 'VEN',
      6: 'SAM',
      7: 'DIM',
    };
    return weekdays[date.weekday] ?? '';
  }

  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  late AnimationController _searchAnimationController;
  late AuthLocalDataSource _authLocalDataSource;

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  // Filtered data for client-side text search
  List<List<EmploiTempsEntity>> _filteredScheduleData = [];
  List<List<EmploiTempsEntity>> _allScheduleData = [];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _authLocalDataSource = sl<AuthLocalDataSource>();
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _searchController.addListener(_filterScheduleEntries);

    // Load schedule data
    _loadScheduleData();
  }

  Future<void> _loadScheduleData() async {
    try {
      // Get the school code from widget or fallback to a default
      final codeEtab = widget.school?.codeEtab ?? '';

      // Get phone number from AuthLocalDataSource
      final telephone = await _authLocalDataSource.getPhoneNumber() ?? '';

      // For ETU users, codeEtudiant is the same as codeUtilisateur
      // For PAR users, codeEtudiant comes from the selected child
      final codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.school?.codeUtilisateur ?? '';
      final codeUtilisateur = widget.etudiant != null ? widget.school?.codeUtilisateur : null;

      if (mounted && codeEtab.isNotEmpty && telephone.isNotEmpty && codeEtudiant.isNotEmpty) {
        context.read<EmploiDuTempsCubit>().loadScheduleData(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );
      } else {
        debugPrint('Missing required parameters for schedule data loading: codeEtab=$codeEtab, telephone=$telephone, codeEtudiant=$codeEtudiant');
      }
    } catch (e) {
      debugPrint('Error loading schedule data: $e');
    }
  }

  void _filterScheduleEntries() async { // Made async to await getPhoneNumber
    final query = _searchController.text.toLowerCase();
    debugPrint("Filtering schedule entries: query=$query, startDateFilter=$_startDateFilter, endDateFilter=$_endDateFilter");
    // Check if both date filters are set
    if (_startDateFilter != null && _endDateFilter != null) {
      try {
        // Get the school code from widget or fallback to a default
        final codeEtab = widget.school?.codeEtab ?? '';

        // Get phone number from AuthLocalDataSource
        final telephone = await _authLocalDataSource.getPhoneNumber() ?? '';

        // For ETU users, codeEtudiant is the same as codeUtilisateur
        // For PAR users, codeEtudiant comes from the selected child
        final codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.school?.codeUtilisateur ?? '';
        final codeUtilisateur = widget.etudiant != null ? widget.school?.codeUtilisateur : null;

        if (mounted && codeEtab.isNotEmpty && telephone.isNotEmpty && codeEtudiant.isNotEmpty) {
          // Call the cubit method to load filtered data
          context.read<EmploiDuTempsCubit>().loadFilteredScheduleData(
            codeEtab: codeEtab,
            telephone: telephone,
            codeEtudiant: codeEtudiant,
            codeUtilisateur: codeUtilisateur,
            startDate: _startDateFilter!,
            endDate: _endDateFilter!,
          );
        } else {
          debugPrint('Missing required parameters for filtered schedule data loading: codeEtab=$codeEtab, telephone=$telephone, codeEtudiant=$codeEtudiant');
        }
      } catch (e) {
        debugPrint('Error loading filtered schedule data: $e');
      }
    } else {
      // Existing logic for text search or no filters
      setState(() {
        if (query.isEmpty) {
          // Reset to original data - will be handled by BLoC state
          _filteredScheduleData = _allScheduleData;
        } else {
          // Apply client-side filtering to current schedule data based on query
          // This will be implemented after BoC integration
          debugPrint("Applying text filter: query=$query");
          _filteredScheduleData = _allScheduleData.map((daySchedule) {
            return daySchedule.where((entry) {
              return entry.cours.toLowerCase().contains(query) ||
                     entry.professeur.toLowerCase().contains(query) ||
                     entry.heure.toLowerCase().contains(query) ||
                     entry.salle.toLowerCase().contains(query) ||
                     entry.classe.toLowerCase().contains(query) ||
                     entry.semestre.toLowerCase().contains(query) ||
                     entry.type.toLowerCase().contains(query) ||
                     entry.date.toLowerCase().contains(query);
            }).toList();
          }).toList();
          // Note: Client-side text filtering logic needs to be added here
          // based on the current state's scheduleData.
        }
      });
    }
  }

  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    debugPrint("startDateFilter: $_startDateFilter, endDateFilter: $_endDateFilter");
    _filterScheduleEntries();
  }

  // Method to clear date filters
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Reapply filters without date constraint
    _filterScheduleEntries();
  }

  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (_isSearchBarVisible) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
        _searchController.clear();
        _startDateFilter = null; // Clear date filters when hidden
        _endDateFilter = null;
        _filteredScheduleData = []; // Reset filtered data
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: BlocListener<EmploiDuTempsCubit, EmploiDuTempsState>(
        listener: (context, state) {
          if (state is EmploiDuTempsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        child: CustomScrollView(
          slivers: [
            CustomAppBar(
              pageSection: HeaderEnum.planning,
              isSearchBarVisible: _isSearchBarVisible,
              title: "EMPLOI DU TEMPS",
              etablissementUtilisateur: widget.school,
              enfantDuTuteur: widget.etudiant,
              onSearchTap: _toggleSearchBarVisibility,
            ),
            AnimatedBuilder(
              animation: _searchAnimationController,
              builder: (context, child) {
                return SliverPersistentHeader(
                  pinned: true,
                  delegate: SearchBarSliver(
                    extentHeight: _searchAnimationController.value * 60.0,
                    searchController: _searchController,
                    onSearchChanged: (query) => _filterScheduleEntries(),
                    onDateFilterChanged: _onDateFilterChanged,
                    onClearDateFilter: _clearDateFilter,
                    hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                    hintText: "Rechercher un cours...",
                  ),
                );
              },
            ),
            BlocBuilder<EmploiDuTempsCubit, EmploiDuTempsState>(
              builder: (context, state) {
                if (state is EmploiDuTempsLoading) {
                  debugPrint("Loading schedule data...");
                  return SliverFillRemaining(
                    child: Center(
                      child: CustomSpinner(
                        size: 60.0,
                        strokeWidth: 5.0,
                      ),
                    ),
                  );
                } else if (state is EmploiDuTempsLoaded) {
                  debugPrint("Schedule data loaded: ${state.scheduleData.length} days");
                  _filteredScheduleData = state.scheduleData;
                  if (_filteredScheduleData.isEmpty) {
                    return SliverFillRemaining(
                      child: Center(
                        child: EmptyMessage(message: "Aucun cours trouvé"),
                      ),
                    );
                  }

                  return SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, dayIndex) {
                        final daySchedule = _filteredScheduleData[dayIndex];
                        if (daySchedule.isEmpty) return const SizedBox.shrink();

                        // Get the date from the first schedule item of the day
                        final date = DateTime.parse(daySchedule.first.date);
                        final weekday = _getWeekdayAbbr(date);
                        final dateStr = "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}";

                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 7.0, horizontal: 1.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Weekday and date column (left)
                              Padding(
                                padding: const EdgeInsets.only(right: 0, top: 4.0, left: 7.0),
                                child: SizedBox(
                                  width: 60,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        weekday,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          letterSpacing: 1.5,
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Text(
                                        dateStr,
                                        style: const TextStyle(
                                          fontSize: 11,
                                          color: Colors.black54,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              // Schedule items column (right)
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: daySchedule.map((scheduleItem) =>
                                    EmploiTempsItem(emploiTemps: scheduleItem)
                                  ).toList(),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      childCount: _filteredScheduleData.length,
                    ),
                  );
                } else if (state is EmploiDuTempsError) {
                  return SliverFillRemaining( 
                    child: Center(
                      child: EmptyMessage(message: "Erreur: ${state.message}"),
                    ),
                  );
                } else {
                  return SliverFillRemaining(
                    child: Center(
                      child: EmptyMessage(message: "Aucune donnée disponible"),
                    ),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
