import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/emploi_temps_entity.dart';
import '../repositories/emplois_du_temps_repository.dart';

/// Parameters for GetEmploisDuTempsUseCase
class GetEmploisDuTempsParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetEmploisDuTempsParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });
}

/// Use case for getting schedule/timetable data
class GetEmploisDuTempsUseCase implements UseCase<List<List<EmploiTempsEntity>>, GetEmploisDuTempsParams> {
  final EmploisDuTempsRepository repository;

  const GetEmploisDuTempsUseCase(this.repository);

  @override
  Future<Either<Failure, List<List<EmploiTempsEntity>>>> call(GetEmploisDuTempsParams params) async {
    return await repository.getEmploisDuTemps(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
