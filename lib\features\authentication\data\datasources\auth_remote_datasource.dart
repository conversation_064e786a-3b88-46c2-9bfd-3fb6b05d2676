import 'package:Kairos/core/device_info.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/api/api_exception.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/services/device_info_service.dart';
import '../models/send_sms_request.dart';
import '../models/sms_response_model.dart'; // Import the new model
import '../models/password_reset_model.dart';
import '../../domain/entities/sms_response_entity.dart'; // Import the new entity
import '../../domain/entities/password_reset_entity.dart';

/// Abstract interface for authentication remote data source
abstract class AuthRemoteDataSource {
  /// Login with email and password
  Future<DeviceInfo> login(String email, String password);
  
  /// Register new user
  Future<void> register(String email, String password, String fullName);
  
  
  /// Logout current user
  Future<void> logout();
  
  /// Send SMS for verification
  Future<SmsResponseEntity> sendSms(String phoneNumber); // Update return type
  
  /// Refresh authentication token
  Future<String> refreshToken();
  
  /// Activate user account
  Future<void> activateAccount(String activationCode);

  /// Resend SMS for verification
  Future<dynamic> resendSms(String phoneNumber);

  /// Verify PIN code with full name and device info
  Future<dynamic> verifyPinWithDetails(String fullName, String otp, Map<String, dynamic> deviceInfo, String phoneNumber, {
    bool tokenExists = false,
  });

  /// Check the user's response to the reactivation dialog
  Future<dynamic> checkResponse(String phoneNumber, {required bool activated});

  /// Send password reset email
  Future<PasswordResetResponseEntity> sendPasswordResetEmail(String email);

  /// Reset password with PIN and new password
  Future<PasswordResetResponseEntity> resetPassword(
    String email,
    String pin,
    String newPassword,
    String confirmPassword,
  );
}

/// Implementation of AuthRemoteDataSource
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final ApiClient apiClient;

  AuthRemoteDataSourceImpl({required this.apiClient});
  
  @override
  Future<DeviceInfo> login(String email, String password) async {
    // TODO: Implement API call
    throw UnimplementedError('Login API call not implemented yet');
  }
  
  @override
  Future<void> register(String email, String password, String fullName) async {
    // TODO: Implement API call
    throw UnimplementedError('Register API call not implemented yet');
  }
  
  @override
  Future<void> logout() async {
    // TODO: Implement API call
    throw UnimplementedError('Logout API call not implemented yet');
  }
  
  @override
  Future<SmsResponseEntity> sendSms(String phoneNumber) async { // Update return type
    try {
      // Get device info from service
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create request payload
      final sendSmsRequest = SendSmsRequest(
        numeroTelephone: phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
      );

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.sendSms,
        data: sendSmsRequest.toJson(),
      );
      
      debugPrint('AuthRemoteDataSourceImpl: send sms response: ${response.data}');

      // Check response status
      if (response.statusCode != 200 && response.statusCode != 201) {
        throw ServerException('Failed to send SMS: ${response.statusMessage}');
      }

      // Parse the response data into SmsResponseModel and return it
      return SmsResponseModel.fromJson(response.data);

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de l\'envoi du SMS: $e');
    }
  }
  
  @override
  Future<String> refreshToken() async {
    // TODO: Implement API call
    throw UnimplementedError('Refresh token API call not implemented yet');
  }
  
  @override
  Future<void> activateAccount(String activationCode) async {
    // TODO: Implement API call
    throw UnimplementedError('Activate account API call not implemented yet');
  }

  @override
  Future<dynamic> resendSms(String phoneNumber) async {
    try {
      // Get device info from service
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create request payload
      final sendSmsRequest = SendSmsRequest(
        numeroTelephone: phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
      );

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.resendSms, // Use the resend SMS endpoint
        data: sendSmsRequest.toJson(),
      );

      debugPrint('AuthRemoteDataSourceImpl: resend sms response: ${response.data}');

      // Check response status
      if (response.statusCode != 200 && response.statusCode != 201) {
        throw ServerException('Failed to resend SMS: ${response.statusMessage}');
      }

      return response.data;

      // Optionally parse response if needed
      // final sendSmsResponse = SendSmsResponse.fromJson(response.data);

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors du renvoi du SMS: $e');
    }
  }

  @override
  Future<dynamic> verifyPinWithDetails(
    String fullName,
    String otp,
    Map<String, dynamic> deviceInfo,
    String phoneNumber, {
    bool tokenExists = false,
  }) async {
    try {
      // Create request payload and endpoint based on tokenExists
      final String endpoint = tokenExists
          ? ApiEndpoints.refreshToken
          : ApiEndpoints.verifyPin;
      final Map<String, dynamic> requestPayload = tokenExists
          ? {
              'codeOtp': otp,
              'marqueTelephone': deviceInfo['marqueTelephone'],
              'modelTelephone': deviceInfo['modelTelephone'],
              'imeiTelephone': deviceInfo['imeiTelephone'],
              'numeroSerie': deviceInfo['numeroSerie'],
              'numeroTelephone': phoneNumber,
            }
          : {
              'nomComplet': fullName,
              'pin': otp,
              'marqueTelephone': deviceInfo['marqueTelephone'],
              'modelTelephone': deviceInfo['modelTelephone'],
              'imeiTelephone': deviceInfo['imeiTelephone'],
              'numeroSerie': deviceInfo['numeroSerie'],
              'numeroTelephone': phoneNumber,
            };

      // Make API call
      final response = await apiClient.post(
        endpoint, // Use the correct endpoint
        data: requestPayload,
      );

      debugPrint('AuthRemoteDataSourceImpl: verify pin response: \\${response.data}');

      // Check response status
      if (response.statusCode != 200 && response.statusCode != 201) {
        // Handle API error response with returnCode and userMessage
        if (response.data is Map<String, dynamic> && response.data.containsKey('returnCode') && response.data.containsKey('userMessage')) {
           throw ServerException(response.data['userMessage'], response.data['returnCode']);
        }
        throw ServerException('Failed to verify PIN: \\${response.statusMessage}');
      }

      return response.data;

    } on DioException catch (e) {
      debugPrint('AuthRemoteDataSourceImpl: verify pin DioException: \\${e}');
      if (e.response != null) {
        // Handle API error response with returnCode and userMessage
        if (e.response!.data is Map<String, dynamic> && e.response!.data.containsKey('returnCode') && e.response!.data.containsKey('userMessage')) {
           throw ServerException(e.response!.data['userMessage'],e.response?.statusCode.toString(), e.response!.data['returnCode']);
        }
        // Handle generic API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      debugPrint('AuthRemoteDataSourceImpl: verify pin catch: $e');
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la vérification du PIN: $e');
    }
  }

  @override
  Future<dynamic> checkResponse(String phoneNumber, {required bool activated}) async {
    try {
      // Get device info from service
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create request payload including device info and activated status
      final requestPayload = {
        'numeroTelephone': phoneNumber,
        'marqueTelephone': deviceInfo.marqueTelephone,
        'modelTelephone': deviceInfo.modelTelephone,
        'imeiTelephone': deviceInfo.imeiTelephone,
        'numeroSerie': deviceInfo.numeroSerie,
        'activated': activated, // Add the activated boolean
      };

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.checkResponse, // Use the check response endpoint
        data: requestPayload,
      );

      debugPrint('AuthRemoteDataSourceImpl: check response: ${response.data}');

      // Check response status
      if (response.statusCode != 200 && response.statusCode != 201) {
        // Handle API error response with returnCode and userMessage
        if (response.data is Map<String, dynamic> && response.data.containsKey('returnCode') && response.data.containsKey('userMessage')) {
           throw ServerException(response.data['userMessage'], response.data['returnCode']);
        }
        throw ServerException('Failed to check response: ${response.statusMessage}');
      }

      return response.data;

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response with returnCode and userMessage
        if (e.response!.data is Map<String, dynamic> && e.response!.data.containsKey('returnCode') && e.response!.data.containsKey('userMessage')) {
           throw ServerException(e.response!.data['userMessage'], e.response!.data['returnCode']);
        }
        // Handle generic API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la vérification de la réponse: $e');
    }
  }

  @override
  Future<PasswordResetResponseEntity> sendPasswordResetEmail(String email) async {
    try {
      // Create request payload
      final Map<String, dynamic> requestPayload = {
        'email': email,
      };

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.forgotPassword, // We'll add this endpoint
        data: requestPayload,
      );

      // Parse response
      final responseModel = PasswordResetResponseModel.fromJson(response.data);
      return responseModel.toEntity();

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de l\'envoi de l\'email: $e');
    }
  }

  @override
  Future<PasswordResetResponseEntity> resetPassword(
    String email,
    String pin,
    String newPassword,
    String confirmPassword,
  ) async {
    try {
      // Create request payload
      final Map<String, dynamic> requestPayload = {
        'email': email,
        'pin': pin,
        'newPassword': newPassword,
        'confirmPassword': confirmPassword,
      };

      // Make API call
      final response = await apiClient.post(
        ApiEndpoints.resetPassword, // We'll add this endpoint
        data: requestPayload,
      );

      // Parse response
      final responseModel = PasswordResetResponseModel.fromJson(response.data);
      return responseModel.toEntity();

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la réinitialisation: $e');
    }
  }
}
