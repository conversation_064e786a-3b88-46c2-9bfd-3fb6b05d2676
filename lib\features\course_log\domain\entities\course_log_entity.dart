import 'package:equatable/equatable.dart';

/// Domain entity for course log (cahier texte) items
class CourseLogEntity extends Equatable {
  final String dateCours;
  final String heureDebutPrevu;
  final String heureFinPrevu;
  final String heureDebutSaisi;
  final String heureFinSaisi;
  final String dureeSaisi;
  final String cours;
  final String professeur;
  final String semestre;
  final String auteurSaisi;
  final String classe;
  final String contenuSaisi;
  final int idObject;
  final String matriculeProfesseur;
  final int idPieceJointe;

  const CourseLogEntity({
    required this.dateCours,
    required this.heureDebutPrevu,
    required this.heureFinPrevu,
    required this.heureDebutSaisi,
    required this.heureFinSaisi,
    required this.dureeSaisi,
    required this.cours,
    required this.professeur,
    required this.semestre,
    required this.auteurSaisi,
    required this.classe,
    required this.contenuSaisi,
    required this.idObject,
    required this.matriculeProfesseur,
    required this.idPieceJointe,
  });

  @override
  List<Object?> get props => [
        dateCours,
        heureDebutPrevu,
        heureFinPrevu,
        heureDebutSaisi,
        heureFinSaisi,
        dureeSaisi,
        cours,
        professeur,
        semestre,
        auteurSaisi,
        classe,
        contenuSaisi,
        idObject,
        matriculeProfesseur,
        idPieceJointe,
      ];
}