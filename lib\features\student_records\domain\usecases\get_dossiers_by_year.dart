import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/core/usecases/usecase.dart';
import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart';
import 'package:Kairos/features/student_records/domain/repositories/dossier_repository.dart';

/// Use case for fetching student dossiers filtered by year.
/// This use case encapsulates the business logic for retrieving student documents
/// based on the academic year, ensuring a clean separation of concerns.
class GetDossiersByYearUseCase implements UseCase<List<DossierEntity>, GetDossiersByYearParams> {
  final DossierRepository repository;

  GetDossiersByYearUseCase(this.repository);

  @override
  Future<Either<Failure, List<DossierEntity>>> call(
      GetDossiersByYearParams params) async {
    return await repository.getDossiersByYear(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      annee: params.annee,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

/// Parameters for [GetDossiersByYearUseCase].
/// This class defines the required parameters for fetching student dossiers
/// by year, ensuring all necessary data is provided for the operation.
class GetDossiersByYearParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String annee;
  final String? codeUtilisateur;

  const GetDossiersByYearParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    required this.annee,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props =>
      [codeEtab, telephone, codeEtudiant, annee, codeUtilisateur];
}