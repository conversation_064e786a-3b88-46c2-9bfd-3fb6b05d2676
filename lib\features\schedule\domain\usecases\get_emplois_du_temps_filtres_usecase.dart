import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/emploi_temps_entity.dart';
import '../repositories/emplois_du_temps_repository.dart';

/// Parameters for GetEmploisDuTempsFiltresUseCase
class GetEmploisDuTempsFiltresParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;
  final String? startDate;
  final String? endDate;

  const GetEmploisDuTempsFiltresParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
    this.startDate,
    this.endDate,
  });
}

/// Use case for getting filtered schedule/timetable data
class GetEmploisDuTempsFiltresUseCase implements UseCase<List<List<EmploiTempsEntity>>, GetEmploisDuTempsFiltresParams> {
  final EmploisDuTempsRepository repository;

  const GetEmploisDuTempsFiltresUseCase(this.repository);

  @override
  Future<Either<Failure, List<List<EmploiTempsEntity>>>> call(GetEmploisDuTempsFiltresParams params) async {
    return await repository.getEmploisDuTempsFiltres(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}
