import 'package:equatable/equatable.dart';
import '../../domain/entities/course_log_entity.dart';

/// Base state for CourseLogCubit
abstract class CourseLogState extends Equatable {
  const CourseLogState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class CourseLogInitial extends CourseLogState {}

/// Loading state
class CourseLogLoading extends CourseLogState {}

/// Loaded state with course log data
class CourseLogLoaded extends CourseLogState {
  final List<List<CourseLogEntity>> courseLogData;

  const CourseLogLoaded({required this.courseLogData});

  @override
  List<Object?> get props => [courseLogData];
}

/// Error state
class CourseLogError extends CourseLogState {
  final String message;

  const CourseLogError({required this.message});

  @override
  List<Object?> get props => [message];
}