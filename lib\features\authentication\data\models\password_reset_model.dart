import '../../domain/entities/password_reset_entity.dart';

/// Model for password reset request
class PasswordResetRequestModel extends PasswordResetRequestEntity {
  const PasswordResetRequestModel({
    required super.email,
  });

  /// Create model from JSON
  factory PasswordResetRequestModel.fromJson(Map<String, dynamic> json) {
    return PasswordResetRequestModel(
      email: json['email'] as String,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }

  /// Convert model to entity
  PasswordResetRequestEntity toEntity() {
    return PasswordResetRequestEntity(
      email: email,
    );
  }
}

/// Model for password reset confirmation
class PasswordResetConfirmModel extends PasswordResetConfirmEntity {
  const PasswordResetConfirmModel({
    required super.email,
    required super.pin,
    required super.newPassword,
    required super.confirmPassword,
  });

  /// Create model from JSON
  factory PasswordResetConfirmModel.fromJson(Map<String, dynamic> json) {
    return PasswordResetConfirmModel(
      email: json['email'] as String,
      pin: json['pin'] as String,
      newPassword: json['newPassword'] as String,
      confirmPassword: json['confirmPassword'] as String,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'pin': pin,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
    };
  }

  /// Convert model to entity
  PasswordResetConfirmEntity toEntity() {
    return PasswordResetConfirmEntity(
      email: email,
      pin: pin,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }
}

/// Model for password reset response
class PasswordResetResponseModel extends PasswordResetResponseEntity {
  const PasswordResetResponseModel({
    required super.success,
    required super.message,
    super.email,
  });

  /// Create model from JSON
  factory PasswordResetResponseModel.fromJson(Map<String, dynamic> json) {
    return PasswordResetResponseModel(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      email: json['email'] as String?,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      if (email != null) 'email': email,
    };
  }

  /// Convert model to entity
  PasswordResetResponseEntity toEntity() {
    return PasswordResetResponseEntity(
      success: success,
      message: message,
      email: email,
    );
  }
}
